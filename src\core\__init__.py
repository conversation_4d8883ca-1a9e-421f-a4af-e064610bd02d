"""
Core Module - 全景影像處理核心演算法庫

本模組提供完整的全景影像處理算法，包含座標轉換、投影變換、
插值算法、立方體映射等核心功能。

主要子模組：
- coordinate: 座標系統轉換
- cube_mapping: 立方體映射處理
- interpolation: 高性能插值算法
- projection: 投影轉換
- samplers: 影像採樣器
"""

import os
# 確保測試環境設定
os.environ["TESTING"] = "1"
os.environ["DISABLE_NUMBA"] = "1"
os.environ["DISABLE_GPU"] = "1"

# 套件資訊
__package__ = "core"
__version__ = "1.1.0"
__author__ = "AI 部門 - 全景處理團隊"

# 只列出可以安全導入的模組
__all__ = [
    "coordinate",
    "cube_mapping", 
    "interpolation",
    "projection",
    "samplers",
]

# 延遲導入 - 只在需要時才導入具體的類別
def __getattr__(name: str):
    """延遲導入以避免循環引用和複雜初始化問題"""
    if name in __all__:
        try:
            from importlib import import_module
            return import_module(f".{name}", __package__)
        except ImportError as e:
            import warnings
            warnings.warn(f"無法載入 core.{name}: {e}", ImportWarning)
            raise ImportError(f"無法載入 core.{name}: {e}")
    
    # 嘗試從子模組導入主要類別
    try:
        if name == "CoordinateTransformer":
            from .coordinate import CoordinateTransformer
            return CoordinateTransformer
        elif name == "CubeMapper":
            from .cube_mapping import CubeMapper
            return CubeMapper
        elif name == "ProjectionCore":
            from .projection import ProjectionCore
            return ProjectionCore
    except ImportError:
        pass
    
    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")