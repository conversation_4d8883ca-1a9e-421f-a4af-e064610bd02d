"""
全域設定管理模組

本模組透過一系列的 `dataclass` 來集中管理全景圖處理系統的所有可配置參數。
它實現了配置的分層結構、檔案的儲存與載入、動態更新以及有效性驗證。
此外，還提供了全域時間戳管理功能，以確保處理流程中檔案命名的一致性。
"""

import json
import logging
import os
from dataclasses import asdict, dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# 從常數模組導入不可變的常數
from . import constants

# 定義專案和設定檔的根目錄，便於路徑管理。
PROJECT_ROOT = Path(__file__).parent.parent
CONFIG_DIR = PROJECT_ROOT / "config"


@dataclass
class PathConfig:
    """
    路徑相關設定。
    集中管理所有輸入、輸出及暫存目錄的路徑。
    """
    input_dir: str = "./input"  # 輸入影像的來源目錄
    output_dir: str = "./output"  # 處理結果的儲存目錄
    model_dir: str = "./models"  # AI模型的存放目錄
    logo_dir: str = "./logos"  # 標誌（Logo）檔案的存放目錄
    temp_dir: str = "./temp"  # 處理過程中的暫存檔案目錄
    log_dir: str = "./logs"  # 日誌檔案的存放目錄

    def __post_init__(self):
        """
        在物件初始化後自動執行，確保必要的輸出和暫存目錄存在。
        """
        for path in [self.output_dir, self.temp_dir, self.log_dir]:
            os.makedirs(path, exist_ok=True)


@dataclass
class ModelConfig:
    """
    AI模型相關設定。
    定義了模型路徑、推論參數及硬體選擇。
    """
    primary_model_path: str = "models/yolo_face_plate.pt"  # 主要偵測模型的路徑
    secondary_model_path: Optional[str] = None  # 可選的次要偵測模型路徑
    conf_threshold: float = 0.05  # 物件偵測的置信度閾值
    iou_threshold: float = 0.3  # 交並比（IoU）閾值，用於NMS
    max_area_ratio: float = 0.03  # 允許的最大偵測框面積比例
    face5_test_conf: float = 0.25  # Face5模型的專用測試置信度
    device: str = "auto"  # 推論設備選擇 ('auto', 'cuda', 'cpu')
    use_amp: bool = True  # 是否啟用自動混合精度（AMP）以加速GPU推論


@dataclass
class LegacyProcessingConfig:
    """
    (已棄用) 影像處理流程相關設定。
    涵蓋了影像尺寸、品質、平行處理、模糊化及標誌疊加等參數。
    將在未來版本中被 ProcessingSettings 取代。
    """
    # --- 影像尺寸設定 ---
    cube_size: int = 2048  # 立方體面的預設邊長（像素）
    slice_size: int = 512  # 處理大圖時的切片大小
    thumbnail_size: tuple = (400, 200)  # 縮圖的尺寸
    preview_size: tuple = (1536, 256)  # 預覽圖的尺寸

    # --- 影像品質與壓縮設定 ---
    image_quality: int = 95  # JPEG 影像的儲存品質 (1-100)
    png_compression: int = 3  # PNG 影像的壓縮等級 (0-9)
    webp_quality: int = 95  # WebP 影像的儲存品質 (1-100)

    # --- 平行與記憶體設定 ---
    max_workers: int = 8  # 平行處理時的最大工作者數量
    batch_size: int = 10  # 批次處理的影像數量
    max_memory_mb: int = 8192  # 系統允許的最大記憶體使用量 (MB)

    # --- 模糊處理設定 ---
    blur_kernel_size: tuple = (51, 51)  # 高斯模糊的核心大小
    mosaic_size: int = 15  # 馬賽克效果的格子大小
    blur_intensity: float = 1.0  # 模糊效果的強度係數
    blur_sigma: float = 15.0  # 高斯模糊的標準差

    # --- 標誌疊加設定 ---
    logo_scale: float = 0.741  # 疊加標誌相對於目標區域的縮放比例
    logo_cache_size: int = 10  # 快取的標誌圖片數量


@dataclass
class SystemConfig:
    """
    系統級設定。
    包括日誌、性能監控和使用者介面等全域性參數。
    """
    # --- 日誌系統設定 ---
    log_level: str = "INFO"  # 日誌記錄等級 (e.g., "DEBUG", "INFO", "WARNING")
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"  # 日誌訊息格式
    log_max_size_mb: int = 10  # 單一誌檔案的最大大小 (MB)
    log_backup_count: int = 5  # 保留的日誌備份檔案數量

    # --- 性能與快取設定 ---
    enable_gpu: bool = True  # 是否全域啟用GPU加速
    enable_cache: bool = True  # 是否全域啟用快取機制
    cache_size_mb: int = 500  # 全域快取的總大小上限 (MB)
    enable_profiling: bool = False  # 是否啟用性能分析器

    # --- 進度顯示設定 ---
    show_progress: bool = True  # 是否在終端顯示處理進度條
    progress_update_interval: float = 0.5  # 進度條更新的最小時間間隔（秒）


@dataclass
class ProcessingSettings:
    """Processing 模組設定"""
    default_mode: str = "single"
    enable_detection: bool = True
    enable_blur: bool = True
    max_workers: int = 4
    batch_size: int = 10

@dataclass
class UtilsSettings:
    """Utils 模組設定"""
    enable_gpu_management: bool = True
    enable_memory_monitoring: bool = True
    gpu_memory_limit: float = 0.8
    memory_pool_size: int = 1024

@dataclass
class Config:
    """
    主配置類別，作為所有設定的容器。
    透過組合其他設定資料類別，形成一個完整的、分層的設定結構。
    """
    paths: PathConfig = field(default_factory=PathConfig)
    model: ModelConfig = field(default_factory=ModelConfig)
    system: SystemConfig = field(default_factory=SystemConfig)
    
    # 新的、重構後的設定
    processing: ProcessingSettings = field(default_factory=ProcessingSettings)
    utils: UtilsSettings = field(default_factory=UtilsSettings)

    # 舊版設定 (用於向後相容)
    legacy_processing: LegacyProcessingConfig = field(default_factory=LegacyProcessingConfig)
    
    mode: constants.ProcessMode = constants.ProcessMode.PANO_CUBE_DETECT_PYRAMID

    # 用於存放任何未在上述類別中明確定義的自訂參數。
    custom_params: Dict[str, Any] = field(default_factory=dict)

    def save(self, filepath: str):
        """
        將當前配置序列化為JSON格式並儲存到檔案。
        """
        config_dict = asdict(self)
        # 將枚舉（Enum）成員轉換為其字串值，以便JSON序列化。
        config_dict["mode"] = self.mode.value

        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(config_dict, f, indent=2, ensure_ascii=False)

    @classmethod
    def load(cls, filepath: str) -> "Config":
        """
        從JSON檔案載入配置，並將其反序列化為一個Config物件。
        """
        with open(filepath, "r", encoding="utf-8") as f:
            config_dict = json.load(f)

        # 將 'mode' 的字串值轉換回 ProcessMode 枚舉成員。
        if "mode" in config_dict:
            config_dict["mode"] = constants.ProcessMode(config_dict["mode"])

        # 遞迴地重建巢狀的dataclass物件。
        if "paths" in config_dict:
            config_dict["paths"] = PathConfig(**config_dict.pop("paths"))
        if "model" in config_dict:
            config_dict["model"] = ModelConfig(**config_dict.pop("model"))
        if "legacy_processing" in config_dict:
            config_dict["legacy_processing"] = LegacyProcessingConfig(
                **config_dict.pop("legacy_processing")
            )
        # 為了相容舊的設定檔，如果 "processing" 存在，將其視為 legacy
        elif "processing" in config_dict:
             config_dict["legacy_processing"] = LegacyProcessingConfig(
                **config_dict.pop("processing")
            )
        
        if "processing" in config_dict:
            config_dict["processing"] = ProcessingSettings(**config_dict.pop("processing"))
        if "utils" in config_dict:
            config_dict["utils"] = UtilsSettings(**config_dict.pop("utils"))
        if "system" in config_dict:
            config_dict["system"] = SystemConfig(**config_dict.pop("system"))

        return cls(**config_dict)

    def update(self, updates: Dict[str, Any]):
        """
        從一個字典批量更新配置參數。
        支援使用點符號（.）進行巢狀更新，例如 'model.conf_threshold'。
        """
        for key, value in updates.items():
            if "." in key:
                # 處理巢狀鍵
                parts = key.split(".")
                obj = self
                for part in parts[:-1]:
                    obj = getattr(obj, part)
                setattr(obj, parts[-1], value)
            else:
                # 處理頂層鍵
                if hasattr(self, key):
                    setattr(self, key, value)
                else:
                    # 如果鍵不存在於任何設定類別中，則存入自訂參數。
                    self.custom_params[key] = value

    def validate(self) -> List[str]:
        """
        驗證當前配置的有效性，返回一個包含所有錯誤訊息的列表。
        """
        errors = []

        # 範例：檢查模型目錄是否存在。
        if self.paths.model_dir and not os.path.exists(self.paths.model_dir):
            errors.append(f"模型目錄不存在: {self.paths.model_dir}")

        # 範例：檢查置信度閾值是否在有效範圍內。
        if not 0 < self.model.conf_threshold <= 1:
            errors.append(
                f"置信度閾值必須在 (0, 1] 範圍內: {self.model.conf_threshold}"
            )

        # 範例：檢查立方體尺寸是否符合要求。
        if self.processing.cube_size % 4 != 0:
            errors.append(f"立方體尺寸必須是 4 的倍數: {self.processing.cube_size}")

        # 範例：檢查影像品質是否在有效範圍內。
        if not 1 <= self.processing.image_quality <= 100:
            errors.append(
                f"圖像品質必須在 [1, 100] 範圍內: {self.processing.image_quality}"
            )

        return errors


@dataclass
class PyramidConfig:
    """
    影像金字塔相關的專用設定資料類別。
    """
    base_size: int = 2048  # 金字塔的基礎尺寸，通常對應HTML5檢視器的基礎層級。
    levels: Optional[Dict[str, int]] = field(
        default_factory=lambda: {
            "large": 2445,  # 大尺寸層級
            "medium": 1222,  # 中等尺寸層級
            "small": 611,  # 小尺寸層級
        }
    )  # 定義每個金字塔層級的名稱和對應的像素尺寸。

    def __post_init__(self):
        """確保 levels 欄位永遠有預設值。"""
        if self.levels is None:
            self.levels = {
                "large": 2445,
                "medium": 1222,
                "small": 611,
            }

    @property
    def sizes(self) -> List[int]:
        """以列表形式返回所有層級的尺寸。"""
        assert self.levels is not None
        return list(self.levels.values())

    def get_level_size(self, level: Union[int, str]) -> int:
        """
        獲取指定層級的尺寸。
        可以透過層級名稱（str）或索引（int）來查詢。
        """
        assert self.levels is not None
        if isinstance(level, int):
            sizes = self.sizes
            return sizes[level] if 0 <= level < len(sizes) else self.base_size
        elif isinstance(level, str):
            return self.levels.get(level, self.base_size)
        else:
            return self.base_size

    def update_size(self, level: str, new_size: int):
        """更新指定層級的尺寸。"""
        assert self.levels is not None
        if level in self.levels:
            self.levels[level] = new_size
        else:
            raise ValueError(f"未知的層級: {level}")

    @classmethod
    def from_dict(cls, config_dict: Dict) -> "PyramidConfig":
        """從字典物件建立 PyramidConfig 實例。"""
        return cls(
            base_size=config_dict.get("base_size", 2048),
            levels=config_dict.get("levels", {}),
        )


# 全域配置實例，採用單例模式（Singleton）管理。
_config: Optional[Config] = None


def get_config() -> Config:
    """
    獲取全域唯一的配置實例。
    如果實例不存在，則創建一個預設的Config物件。
    """
    global _config
    if _config is None:
        _config = Config()
    return _config


def set_config(config: Config):
    """
    設置一個新的全域配置實例。
    """
    global _config
    _config = config


def load_config(filepath: str) -> Config:
    """
    從檔案載入配置，並將其設定為新的全域配置。
    """
    config = Config.load(filepath)
    set_config(config)
    return config


def reset_config():
    """
    將全域配置重置為預設值。
    """
    global _config
    _config = Config()
    return _config


# --- 便捷訪問函式 ---
def get_path_config() -> PathConfig:
    """快捷獲取路徑配置。"""
    return get_config().paths


def get_model_config() -> ModelConfig:
    """快捷獲取模型配置。"""
    return get_config().model


def get_processing_config() -> LegacyProcessingConfig:
    """(已棄用) 快捷獲取舊版處理配置。"""
    return get_config().legacy_processing

def get_processing_settings() -> ProcessingSettings:
    """快捷獲取新版 Processing 模組設定。"""
    return get_config().processing

def get_utils_settings() -> UtilsSettings:
    """快捷獲取 Utils 模組設定。"""
    return get_config().utils


def get_system_config() -> SystemConfig:
    """快捷獲取系統配置。"""
    return get_config().system


# 建立一個預設配置的實例，可供參考或直接使用。
DEFAULT_CONFIG = Config()


# ========== 時間戳記管理 ==========
_GLOBAL_TIMESTAMP: Optional[str] = None


def get_global_timestamp() -> str:
    """
    獲取一個全域唯一的、格式化的時間戳記（例如 "20231027_153000"）。
    在同一次程式執行期間，此時間戳記保持不變，確保所有相關檔案都使用相同標記。
    """
    global _GLOBAL_TIMESTAMP
    if _GLOBAL_TIMESTAMP is None:
        _GLOBAL_TIMESTAMP = datetime.now().strftime("%Y%m%d_%H%M%S")
    return _GLOBAL_TIMESTAMP


def reset_global_timestamp() -> str:
    """
    重置全域時間戳記，並返回一個新的當前時間戳記。
    適用於開始新一批次處理任務的場景。
    """
    global _GLOBAL_TIMESTAMP
    _GLOBAL_TIMESTAMP = datetime.now().strftime("%Y%m%d_%H%M%S")
    return _GLOBAL_TIMESTAMP


def generate_timestamped_filename(prefix: str, extension: str = "") -> str:
    """
    根據全域時間戳記生成帶時間戳的檔案名稱。

    Args:
        prefix: 檔案名稱的前綴。
        extension: 檔案的副檔名（例如 '.csv', '.log'）。

    Returns:
        一個格式如 'prefix_YYYYMMDD_HHMMSS.extension' 的檔案名稱字串。
    """
    timestamp = get_global_timestamp()
    if extension and not extension.startswith("."):
        extension = "." + extension
    return f"{prefix}_{timestamp}{extension}"


def get_progress_filename() -> str:
    """快捷獲取帶時間戳的進度檔案名稱。"""
    return generate_timestamped_filename("progress", ".csv")


def get_log_filename() -> str:
    """快捷獲取帶時間戳的日誌檔案名稱。"""
    return generate_timestamped_filename("program_log", ".log")


def get_blur_stats_filename() -> str:
    """快捷獲取帶時間戳的模糊統計檔案名稱。"""
    return generate_timestamped_filename("blur_stats", ".csv")


if __name__ == "__main__":
    # 此區塊的程式碼僅在直接執行此腳本時運行，用於測試和展示模組功能。
    print("=== 配置管理模組使用範例 ===")
    print("\n基本使用：")
    print("config = get_config()")
    print("config.model.conf_threshold = 0.6")
    print("config.save('my_config.json')")
    print("\n載入配置：")
    print("config = load_config('my_config.json')")
    print("\n驗證配置：")
    print("errors = config.validate()")
    print("if errors:")
    print("    print('配置錯誤:', errors)")
    print("\n時間戳記功能：")
    print(f"全域時間戳記: {get_global_timestamp()}")
    print(f"進度檔案名: {get_progress_filename()}")
    print(f"日誌檔案名: {get_log_filename()}")
    print(f"模糊統計檔案名: {get_blur_stats_filename()}")
