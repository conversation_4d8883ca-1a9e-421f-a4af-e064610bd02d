"""
檔案處理工具模組

提供檔案和目錄相關的工具函數，支援：
- 目錄掃描和檢索
- 進度記錄和讀取
- CSV 報告處理
- 檔案驗證和管理
- 路徑處理工具
"""


import os
import csv
import time
import logging
import glob
from typing import Any
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
import sys

# Import project modules directly (using pip install -e .)
from config.constants import CURRENT_PROGRESS_FILE
from config.constants import SUPPORTED_IMAGE_EXTS

logger = logging.getLogger(__name__)


class ProcessingStatus(Enum):
    """處理狀態列舉"""
    PENDING = "待處理"
    PROCESSING = "處理中"
    COMPLETED = "完成"
    FAILED = "失敗"
    SKIPPED = "跳過"


@dataclass
class ProgressRecord:
    """進度記錄數據結構"""
    district: str
    scene: str
    status: ProcessingStatus
    timestamp: str
    additional_info: dict[str, Any] | None = None


@dataclass
class SceneStatistics:
    """場景統計數據結構"""
    district: str
    scene: str
    blur_count: int
    face_counts: list[int] | None = None

    def __post_init__(self):
        if self.face_counts is None:
            self.face_counts = [0] * 6  # 6個立方體面


class FileUtils:
    """
    檔案處理工具類

    提供檔案和目錄操作的便利方法。
    """

    @staticmethod
    def ensure_directory(directory_path: str) -> bool:
        """
        確保目錄存在，如不存在則創建

        Args:
            directory_path: 目錄路徑

        Returns:
            是否成功
        """
        try:
            os.makedirs(directory_path, exist_ok=True)
            return True
        except Exception as e:
            logger.error(f"創建目錄失敗 {directory_path}: {e}")
            return False

    @staticmethod
    def get_valid_image_files(directory: str, pattern: str = "*") -> list[str]:
        """
        獲取目錄中的有效圖像文件

        Args:
            directory: 目錄路徑
            pattern: 檔案匹配模式

        Returns:
            有效圖像文件路徑列表
        """
        if not os.path.exists(directory):
            return []

        image_files = []

        for ext in SUPPORTED_IMAGE_EXTS:
            search_pattern = os.path.join(directory, f"{pattern}{ext}")
            files = glob.glob(search_pattern)
            image_files.extend(files)

        return sorted(image_files)

    @staticmethod
    def scan_district_folders(base_path: str) -> list[str]:
        """
        掃描基礎路徑中所有包含「區」字的目錄

        Args:
            base_path: 基礎目錄路徑

        Returns:
            區域資料夾路徑列表
        """
        if not os.path.exists(base_path):
            logger.warning(f"基礎路徑不存在: {base_path}")
            return []

        district_folders = []

        try:
            for item in os.listdir(base_path):
                item_path = os.path.join(base_path, item)
                if os.path.isdir(item_path) and '區' in item:
                    district_folders.append(item_path)

            logger.info(f"掃描到 {len(district_folders)} 個區域資料夾")
            return sorted(district_folders)

        except Exception as e:
            logger.error(f"掃描區域資料夾失敗: {e}")
            return []

    @staticmethod
    def find_resource_path(base_path: str, resource_type: str,
                           city_code: str | None = None) -> str | None:
        """
        查找資源文件路徑（如 logo、模型等）

        Args:
            base_path: 基礎路徑
            resource_type: 資源類型 ('logo', 'models', 'config')
            city_code: 城市代號（用於 logo 查找）

        Returns:
            資源文件路徑
        """
        possible_dirs = [
            os.path.join(base_path, resource_type),
            os.path.join(base_path, resource_type.rstrip('s')),  # 去掉複數
            os.path.join(os.path.dirname(base_path), resource_type),
            os.path.join(os.path.dirname(base_path), resource_type.rstrip('s'))
        ]

        for resource_dir in possible_dirs:
            if not os.path.exists(resource_dir):
                continue

            # 查找相關文件
            found_path = FileUtils._search_resource_in_dir(
                resource_dir, resource_type, city_code)
            if found_path:
                return found_path

        logger.warning(f"未找到 {resource_type} 資源")
        return None

    @staticmethod
    def _search_resource_in_dir(directory: str, resource_type: str,
                                city_code: str | None = None) -> str | None:
        """在指定目錄中搜索資源文件"""
        try:
            for item in os.listdir(directory):
                item_path = os.path.join(directory, item)

                if os.path.isfile(item_path):
                    # 根據資源類型檢查文件
                    if resource_type == 'models' and item.endswith('.pt'):
                        logger.info(f"找到模型文件: {item_path}")
                        return item_path

                    elif resource_type == 'logo' and city_code:
                        if city_code.lower() in item.lower():
                            logger.info(f"找到 {city_code} 的 logo: {item_path}")
                            return item_path

                elif os.path.isdir(item_path):
                    # 遞歸搜索子目錄
                    found = FileUtils._search_resource_in_dir(
                        item_path, resource_type, city_code)
                    if found:
                        return found

        except Exception as e:
            logger.error(f"搜索資源文件失敗: {e}")

        return None

    @staticmethod
    def validate_file_path(file_path: str, expected_extensions: list[str] | None = None) -> bool:
        """
        驗證文件路徑的有效性

        Args:
            file_path: 文件路徑
            expected_extensions: 期望的文件副檔名列表

        Returns:
            是否有效
        """
        if not file_path or not os.path.exists(file_path):
            return False

        if not os.path.isfile(file_path):
            return False

        if expected_extensions:
            ext = os.path.splitext(file_path)[1].lower()
            return ext in expected_extensions

        return True

    @staticmethod
    def get_file_info(file_path: str) -> dict[str, str | int | float]:
        """
        獲取文件信息

        Args:
            file_path: 文件路徑

        Returns:
            文件信息字典
        """
        try:
            stat = os.stat(file_path)
            return {
                'name': os.path.basename(file_path),
                'path': file_path,
                'size': stat.st_size,
                'modified': stat.st_mtime,
                'created': stat.st_ctime,
                'extension': os.path.splitext(file_path)[1].lower()
            }
        except Exception as e:
            logger.error(f"獲取文件信息失敗 {file_path}: {e}")
            return {}


class ProgressManager:
    """
    進度管理器

    管理處理進度的記錄和讀取。
    """

    def __init__(self, output_path: str, progress_filename: str | None = None):
        """
        初始化進度管理器

        Args:
            output_path: 輸出目錄路徑
            progress_filename: 進度文件名
        """
        self.output_path = output_path
        self.progress_filename = progress_filename

        # 確保輸出目錄存在
        FileUtils.ensure_directory(output_path)

    def save_progress(self, district: str, scene: str,
                      status: ProcessingStatus,
                      additional_info: dict[str, Any] | None = None) -> bool:
        """
        保存處理進度

        Args:
            district: 區域名稱
            scene: 場景名稱
            status: 處理狀態
            additional_info: 額外信息

        Returns:
            是否成功
        """
        try:
            # 確定進度文件路徑
            if self.progress_filename is None:
                from ..config.settings import GLOBAL_TIMESTAMP
                global CURRENT_PROGRESS_FILE

                if CURRENT_PROGRESS_FILE is None:
                    CURRENT_PROGRESS_FILE = f'progress_{GLOBAL_TIMESTAMP}.csv'

                progress_path = os.path.join(
                    self.output_path, CURRENT_PROGRESS_FILE)
            else:
                progress_path = os.path.join(
                    self.output_path, self.progress_filename)

            # 檢查文件是否存在
            file_exists = os.path.exists(progress_path)

            with open(progress_path, 'a', encoding='utf-8-sig') as f:
                if not file_exists:
                    f.write("地區,場景,狀態,處理時間\n")

                timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                f.write(f"{district},{scene},{status.value},{timestamp}\n")

            logger.debug(f"進度已保存: {district}/{scene} - {status.value}")
            return True

        except Exception as e:
            logger.error(f"保存進度失敗: {e}")
            return False

    def load_progress(self, specific_file: str | None = None) -> ProgressRecord | None:
        """
        讀取處理進度

        Args:
            specific_file: 指定的進度文件名

        Returns:
            最新的進度記錄
        """
        try:
            progress_file = self._find_progress_file(specific_file)
            if not progress_file:
                return None

            with open(progress_file, 'r', encoding='utf-8-sig') as f:
                lines = f.readlines()

            if len(lines) <= 1:  # 只有標題行或空文件
                return None

            # 找出最後一個非空行
            last_line = None
            for line in reversed(lines):
                if line.strip():
                    last_line = line.strip()
                    break

            if not last_line:
                return None

            parts = last_line.split(',')
            if len(parts) >= 4:
                return ProgressRecord(
                    district=parts[0],
                    scene=parts[1],
                    status=ProcessingStatus(parts[2]),
                    timestamp=parts[3]
                )

        except Exception as e:
            logger.error(f"讀取進度失敗: {e}")

        return None

    def _find_progress_file(self, specific_file: str | None = None) -> str | None:
        """查找進度文件"""
        if specific_file:
            progress_file = os.path.join(self.output_path, specific_file)
            if os.path.exists(progress_file):
                return progress_file
            else:
                logger.warning(f"指定的進度文件不存在: {progress_file}")
                return None

        # 查找最新的進度文件
        csv_files = [f for f in os.listdir(self.output_path)
                     if f.startswith('progress_') and f.endswith('.csv')]

        if not csv_files:
            return None

        # 按修改時間排序，最新的在前
        csv_files.sort(
            key=lambda x: os.path.getmtime(os.path.join(self.output_path, x)),
            reverse=True
        )

        progress_file = os.path.join(self.output_path, csv_files[0])
        logger.info(f"使用最新的進度文件: {progress_file}")
        return progress_file

    def get_completion_status(self, base_path: str) -> tuple[bool, list[str]]:
        """
        檢查是否所有區域都已處理完成

        Args:
            base_path: 輸入基礎路徑

        Returns:
            (是否全部完成, 未完成區域列表)
        """
        # 獲取所有原始區域
        all_districts = FileUtils.scan_district_folders(base_path)
        all_district_names = [os.path.basename(d) for d in all_districts]

        # 獲取已處理的區域
        processed_districts = []
        if os.path.exists(self.output_path):
            for item in os.listdir(self.output_path):
                item_path = os.path.join(self.output_path, item)
                if os.path.isdir(item_path) and '區' in item:
                    processed_districts.append(item)

        # 檢查未處理的區域
        unprocessed = [
            d for d in all_district_names if d not in processed_districts]

        is_complete = len(unprocessed) == 0
        return is_complete, unprocessed


class CSVProcessor:
    """
    CSV 處理器

    處理各種 CSV 報告和統計文件。
    """

    @staticmethod
    def parse_scenes_csv(csv_path: str, include_mode: bool = True) -> tuple[dict[str, list[str]], bool]:
        """
        解析包含兩欄（區、場景）的 CSV 文件

        Args:
            csv_path: CSV 文件路徑
            include_mode: True表示處理清單內場景，False表示處理清單外場景

        Returns:
            (場景字典, 處理模式)
        """
        if not os.path.exists(csv_path):
            logger.error(f"找不到 CSV 文件: {csv_path}")
            return {}, include_mode

        specified_scenes = {}

        try:
            with open(csv_path, 'r', encoding='utf-8-sig') as f:
                csv_reader = csv.reader(f)
                header = next(csv_reader, None)  # 跳過標題行

                for row in csv_reader:
                    if len(row) >= 2:
                        district = row[0].strip()
                        scene = row[1].strip()

                        if district not in specified_scenes:
                            specified_scenes[district] = []

                        specified_scenes[district].append(scene)

            mode_str = "內" if include_mode else "外"
            logger.info(f"從 CSV 文件中讀取到 {len(specified_scenes)} 個區域的指定場景，"
                        f"將處理清單{mode_str}的場景")
            return specified_scenes, include_mode

        except Exception as e:
            logger.error(f"解析 CSV 文件時發生錯誤: {e}")
            return {}, include_mode

    @staticmethod
    def parse_folder_check_report(report_path: str) -> dict[str, list[str]]:
        """
        解析 folder_check_report.csv 文件

        Args:
            report_path: 報告文件路徑

        Returns:
            缺失場景字典 {區域名稱: [場景名稱列表]}
        """
        if not os.path.exists(report_path):
            logger.error(f"找不到報告文件: {report_path}")
            return {}

        missing_scenes = {}

        try:
            with open(report_path, 'r', encoding='utf-8-sig') as f:
                lines = f.readlines()

            current_district = None

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 解析區域行
                if '區域' in line and '場景' in line:
                    parts = line.split(',')
                    if len(parts) >= 2:
                        current_district = parts[0].strip()
                        missing_scenes[current_district] = []

                # 解析場景行
                elif current_district and '區' in line and '_' in line:
                    parts = line.split(',')
                    if len(parts) >= 2:
                        scene_name = parts[1].strip()
                        if scene_name and scene_name != '場景':
                            missing_scenes[current_district].append(scene_name)

            logger.info(f"從報告中解析出 {len(missing_scenes)} 個區域的缺失場景")
            return missing_scenes

        except Exception as e:
            logger.error(f"解析報告文件時發生錯誤: {e}")
            return {}

    @staticmethod
    def record_blur_statistics(district_name: str, scene_name: str,
                               blur_counts: dict[int, int], output_path: str) -> bool:
        """
        記錄場景中每個面的模糊區域數量到 CSV

        Args:
            district_name: 區域名稱
            scene_name: 場景名稱
            blur_counts: 模糊區域計數 {面索引: 數量}
            output_path: 輸出目錄路徑

        Returns:
            是否成功
        """
        try:
            # 確保區域目錄存在
            district_path = os.path.join(output_path, district_name)
            FileUtils.ensure_directory(district_path)

            csv_path = os.path.join(district_path, 'blur_statistics.csv')
            file_exists = os.path.exists(csv_path)

            with open(csv_path, 'a', encoding='utf-8-sig') as f:
                if not file_exists:
                    f.write("場景名稱,前面(F),右面(R),後面(B),左面(L),上面(U),下面(D),總模糊區域數\n")

                # 準備每個面的模糊區域數量
                face_counts = [str(blur_counts.get(i, 0)) for i in range(6)]
                total_blurs = sum(blur_counts.values())

                # 寫入數據
                line = f"{scene_name},{','.join(face_counts)},{total_blurs}\n"
                f.write(line)

            logger.debug(f"已記錄場景 {scene_name} 的模糊區域統計")
            return True

        except Exception as e:
            logger.error(f"記錄模糊區域統計時發生錯誤: {e}")
            return False

    @staticmethod
    def create_blur_summary_report(output_path: str, log_path: str | None = None,
                                   base_path: str | None = None) -> str | None:
        """
        創建模糊區域統計總表

        Args:
            output_path: 輸出目錄路徑
            log_path: 日誌目錄路徑
            base_path: 原始資料路徑

        Returns:
            總表文件路徑
        """
        if log_path is None:
            log_path = output_path

        try:
            # 收集所有場景數據（去重）
            scene_dict = {}
            district_processed_scenes = {}

            # 遍歷所有區域目錄
            for district_item in os.listdir(output_path):
                district_path = os.path.join(output_path, district_item)
                if not os.path.isdir(district_path) or '區' not in district_item:
                    continue

                # 計算處理場景數
                processed_scenes = sum(1 for item in os.listdir(district_path)
                                       if os.path.isdir(os.path.join(district_path, item)))
                district_processed_scenes[district_item] = processed_scenes

                # 讀取模糊統計
                stats_file = os.path.join(district_path, 'blur_statistics.csv')
                if not os.path.exists(stats_file):
                    continue

                CSVProcessor._process_district_stats(
                    stats_file, district_item, scene_dict)

            # 生成報告
            return CSVProcessor._generate_summary_reports(
                scene_dict, district_processed_scenes, log_path, base_path, output_path)

        except Exception as e:
            logger.error(f"創建模糊區域統計總表時發生錯誤: {e}")
            return None

    @staticmethod
    def _process_district_stats(stats_file: str, district_name: str, scene_dict: dict):
        """處理單個區域的統計數據"""
        try:
            with open(stats_file, 'r', encoding='utf-8') as f:
                csv_reader = csv.reader(f)
                header = next(csv_reader, None)  # 跳過標題行

                for row in csv_reader:
                    if len(row) >= 8:  # 場景名稱 + 6個面 + 總數
                        scene_name = row[0]
                        scene_total_blur = int(row[7])

                        if scene_total_blur > 0:
                            key = f"{district_name}_{scene_name}"

                            # 去重處理
                            if key in scene_dict:
                                if scene_total_blur > scene_dict[key]['blur_count']:
                                    scene_dict[key] = {
                                        'district': district_name,
                                        'scene': scene_name,
                                        'blur_count': scene_total_blur
                                    }
                            else:
                                scene_dict[key] = {
                                    'district': district_name,
                                    'scene': scene_name,
                                    'blur_count': scene_total_blur
                                }
        except Exception as e:
            logger.error(f"處理統計文件失敗 {stats_file}: {e}")

    @staticmethod
    def _generate_summary_reports(scene_dict: dict, district_processed_scenes: dict,
                                  log_path: str, base_path: str | None,
                                  output_path: str) -> str:
        """生成匯總報告"""
        current_time = time.strftime("%Y%m%d_%H%M%S")

        # 場景詳細報告
        summary_file = os.path.join(
            log_path, f'blur_summary_{current_time}.csv')
        all_scenes_data = list(scene_dict.values())

        with open(summary_file, 'w', encoding='utf-8-sig') as f:
            f.write("行政區,場景,模糊數量\n")
            for scene_data in sorted(all_scenes_data, key=lambda x: (x['district'], x['scene'])):
                f.write(
                    f"{scene_data['district']},{scene_data['scene']},{scene_data['blur_count']}\n")

        # 區域匯總報告
        district_summary_file = os.path.join(
            log_path, f'district_summary_{current_time}.csv')
        CSVProcessor._generate_district_summary(
            district_summary_file, all_scenes_data, district_processed_scenes,
            base_path, output_path)

        logger.info(f"已創建模糊區域統計總表: {summary_file}")
        return summary_file

    @staticmethod
    def _generate_district_summary(summary_file: str, all_scenes_data: list[dict],
                                   district_processed_scenes: dict, base_path: str | None,
                                   output_path: str):
        """生成區域匯總統計"""
        # 計算每個區域的總場景數
        district_total_scenes = {}
        if base_path:
            district_folders = FileUtils.scan_district_folders(base_path)
            for district_path in district_folders:
                district_name = os.path.basename(district_path)
                scene_count = sum(1 for item in os.listdir(district_path)
                                  if os.path.isdir(os.path.join(district_path, item)))
                district_total_scenes[district_name] = scene_count
        else:
            district_total_scenes = district_processed_scenes

        # 統計每個區域的場景數和總模糊數
        district_stats = {}
        for scene_data in all_scenes_data:
            district = scene_data['district']
            if district not in district_stats:
                district_stats[district] = {'scenes': 0, 'total_blur': 0}

            district_stats[district]['scenes'] += 1
            district_stats[district]['total_blur'] += scene_data['blur_count']

        # 寫入區域匯總統計
        with open(summary_file, 'w', encoding='utf-8-sig') as f:
            f.write("行政區,總場景數,總處理場景數,總模糊數量,平均每場景模糊數\n")

            total_scenes = total_processed_scenes = total_blur = 0

            for district, stats in sorted(district_stats.items()):
                total_scene_count = district_total_scenes.get(district, 0)
                processed_scene_count = district_processed_scenes.get(
                    district, 0)
                avg_blur = stats['total_blur'] / \
                    stats['scenes'] if stats['scenes'] > 0 else 0

                f.write(f"{district},{total_scene_count},{processed_scene_count},"
                        f"{stats['total_blur']},{avg_blur:.2f}\n")

                total_scenes += total_scene_count
                total_processed_scenes += processed_scene_count
                total_blur += stats['total_blur']

            # 總計行
            blur_scenes = sum(stats['scenes']
                              for stats in district_stats.values())
            total_avg = total_blur / blur_scenes if blur_scenes > 0 else 0
            f.write(
                f"總計,{total_scenes},{total_processed_scenes},{total_blur},{total_avg:.2f}\n")


def main():
    """
    模組使用示例
    """
    print("=== 檔案處理工具模組 ===")
    print("\n功能說明：")
    print("1. 目錄掃描和檢索")
    print("2. 進度記錄和管理")
    print("3. CSV 報告處理")
    print("4. 檔案驗證和信息獲取")
    print("5. 資源文件查找")
    print("6. 路徑處理工具")

    print("\n使用示例：")
    print("```python")
    print("from utils.file_utils import FileUtils, ProgressManager, CSVProcessor")
    print("from utils.file_utils import ProcessingStatus")
    print()
    print("# 檔案工具使用")
    print("# 掃描區域資料夾")
    print("districts = FileUtils.scan_district_folders('/data/panoramas')")
    print("print(f'找到 {len(districts)} 個區域')")
    print()
    print("# 查找資源文件")
    print("logo_path = FileUtils.find_resource_path('/base', 'logo', 'tpv')")
    print("model_path = FileUtils.find_resource_path('/base', 'models')")
    print()
    print("# 獲取有效圖像文件")
    print("images = FileUtils.get_valid_image_files('/images', '*.jpg')")
    print()
    print("# 進度管理")
    print("progress_mgr = ProgressManager('/output')")
    print()
    print("# 保存進度")
    print("progress_mgr.save_progress(")
    print("    district='中正區',")
    print("    scene='scene_001',")
    print("    status=ProcessingStatus.COMPLETED")
    print(")")
    print()
    print("# 讀取進度")
    print("last_progress = progress_mgr.load_progress()")
    print("if last_progress:")
    print("    print(f'上次處理: {last_progress.district}/{last_progress.scene}')")
    print()
    print("# 檢查完成狀態")
    print("is_complete, remaining = progress_mgr.get_completion_status('/input')")
    print("print(f'處理完成: {is_complete}, 剩餘: {len(remaining)} 個區域')")
    print()
    print("# CSV 處理")
    print("# 解析場景清單")
    print("scenes, mode = CSVProcessor.parse_scenes_csv('scenes.csv')")
    print()
    print("# 記錄模糊統計")
    print("blur_counts = {0: 2, 1: 1, 5: 3}  # 面索引: 模糊數量")
    print("CSVProcessor.record_blur_statistics(")
    print("    '中正區', 'scene_001', blur_counts, '/output'")
    print(")")
    print()
    print("# 生成統計報告")
    print("report_path = CSVProcessor.create_blur_summary_report('/output')")
    print("print(f'統計報告: {report_path}')")
    print("```")

    print("\n主要類別：")
    print("- FileUtils: 通用檔案操作工具")
    print("- ProgressManager: 進度記錄和管理")
    print("- CSVProcessor: CSV 文件處理")
    print("- ProcessingStatus: 處理狀態枚舉")
    print("- ProgressRecord: 進度記錄數據結構")

    # 簡單測試
    print("\n測試示例：")

    # 測試檔案工具
    test_dir = "/tmp/test_panorama"
    FileUtils.ensure_directory(test_dir)
    print(f"創建測試目錄: {test_dir}")

    # 測試文件驗證
    valid = FileUtils.validate_file_path(__file__, ['.py'])
    print(f"文件驗證測試: {'通過' if valid else '失敗'}")

    # 測試進度管理器
    try:
        progress_mgr = ProgressManager(test_dir)
        success = progress_mgr.save_progress(
            "測試區", "測試場景", ProcessingStatus.COMPLETED)
        print(f"進度保存測試: {'成功' if success else '失敗'}")

        last_progress = progress_mgr.load_progress()
        if last_progress:
            print(
                f"進度讀取測試: {last_progress.district} - {last_progress.status.value}")
    except Exception as e:
        print(f"進度管理測試失敗: {e}")

    print("檔案處理工具測試完成")


if __name__ == "__main__":
    main()
