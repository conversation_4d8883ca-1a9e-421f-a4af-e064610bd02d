"""
配置模組 - 系統設定與常數

此模組為整個系統的配置核心，集中管理所有配置參數、常數定義及設定管理功能。
透過此模組，可以方便地存取和修改系統的各項設定。

主要組件:
- constants: 定義系統中使用的所有靜態常數，如立方體面（Face）、儲存模式（SaveMode）、支援的檔案格式等。
- settings: 提供一個全局的、多層級的配置管理系統，允許動態載入和儲存設定。
- interpolation: 管理與影像插值算法相關的詳細設定。
- detection_classes.txt: 一個外部設定檔，用於定義AI模型可檢測的物件類別。
"""

# 為 PyInstaller 提供明確的套件標識，確保在打包為可執行檔時能正確找到模組。
__package__ = "config"
__version__ = "1.0.0"

# 定義此模組公開的子模組列表，當使用 'from config import *' 時，只有這些模組會被匯入。
__all__ = [
    # 子模組
    "constants", 
    "settings", 
    "interpolation",
    # 主要類別和枚舉 (將在下方動態添加)
]


# 確保此模組被 Python 解譯器正確識別為一個套件。
def _ensure_package():
    """確保此模組被正確識別為一個套件。"""
    return True


_package_initialized = _ensure_package()

# 嘗試匯入便捷的項目，即使失敗也不會中斷程式執行。
# 這種設計使得模組在不同環境下更具彈性。
try:
    from . import constants, interpolation, settings

    # 嘗試從子模組中匯入常用的類別和常數，以便於從頂層模組直接存取。
    # 這是一種提供便捷API的常見模式。
    try:
        # 從 constants 匯入主要常數和枚舉
        from .constants import (
            DETECTION_CLASSES, 
            DETECTION_COLORS,
            SUPPORTED_IMAGE_EXTS, 
            SUPPORTED_LABEL_EXTS,
            DEFAULT_CONF_THRESHOLD,
            DEFAULT_IOU_THRESHOLD,
            DEFAULT_CUBE_SIZE,
            DEFAULT_PYRAMID_LEVELS,
            DEFAULT_TILE_SIZE,
            Face, 
            ProcessMode,
            SaveMode,
            InterpolationMode,
            ErrorCode,
            ProcessingStatus,
        )
        
        # 從 interpolation 匯入插值相關配置
        from .interpolation import (
            InterpolationConfig, 
            InterpolationMethod,
            MultipleInterpolationStrategy,
            MultipleInterpolationConfig,
        )
        
        # 從 settings 匯入配置類別
        from .settings import (
            PyramidConfig,
            Config,
            PathConfig,
            ModelConfig,
            SystemConfig,
            ProcessingSettings,
            UtilsSettings,
            get_config,
        )

        # 將這些便捷匯入的名稱加入 __all__ 列表，使其可以被外部 'from config import *' 匯入。
        __all__.extend([
            # 常數和枚舉
            "Face",
            "SaveMode", 
            "ProcessMode",
            "InterpolationMode",
            "ErrorCode",
            "ProcessingStatus",
            "SUPPORTED_IMAGE_EXTS",
            "SUPPORTED_LABEL_EXTS", 
            "DEFAULT_CONF_THRESHOLD",
            "DEFAULT_IOU_THRESHOLD",
            "DEFAULT_CUBE_SIZE",
            "DEFAULT_PYRAMID_LEVELS",
            "DEFAULT_TILE_SIZE",
            "DETECTION_CLASSES",
            "DETECTION_COLORS",
            # 插值配置
            "InterpolationConfig",
            "InterpolationMethod",
            "MultipleInterpolationStrategy", 
            "MultipleInterpolationConfig",
            # 設定配置
            "PyramidConfig",
            "Config",
            "PathConfig",
            "ModelConfig", 
            "SystemConfig",
            "ProcessingSettings",
            "UtilsSettings",
            "get_config",
        ])
    except ImportError:
        # 如果子模組中的特定常數或類別不存在，則靜默忽略。
        # 這有助於保持向後相容性或處理可選依賴。
        pass

except ImportError as e:
    import sys

    # 處理模組匯入失敗的異常情況。
    # 如果在 PyInstaller 打包的環境中，可能是正常現象，僅打印警告。
    if hasattr(sys, "_MEIPASS"):  # 檢查是否在 PyInstaller 執行環境
        print(f"配置模組匯入警告 (PyInstaller 環境): {e}")
    else:
        # 在標準 Python 環境中，打印更通用的警告訊息。
        print(f"配置模組匯入警告: {e}")
