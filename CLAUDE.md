# CLAUDE.md

此檔案為 Claude Code (claude.ai/code) 在此程式碼庫中工作時提供指導說明。

## 專案概述

這是一個企業級 360 度全景影像處理系統，處理大規模全景影像立方體映射、AI 檢測與隱私保護。整合現代 AI 技術與高效能演算法，提供企業開發標準。

## 開發環境設置

### 前置需求
- Python >= 3.8 (建議使用 Python 3.10+ 以獲得最佳效能)
- 記憶體：8GB+ RAM (建議 16GB+)
- GPU：4GB+ VRAM (選用，用於加速)

### 安裝步驟
```bash
# 建立虛擬環境
python -m venv panorama_env

# 啟動環境
# Windows：
panorama_env\Scripts\activate
# Linux/Mac：
source panorama_env/bin/activate

# 安裝相依性套件
pip install -r requirements.txt

# GPU 支援 (選用)
pip install torch --index-url https://download.pytorch.org/whl/cu118
```

### 常用指令

#### 測試
```bash
# 執行所有測試
python test/run_tests.py

# 執行特定模組測試
python test/run_tests.py --module core

# 執行覆蓋率測試
python test/run_tests.py --coverage --html

# 直接使用 pytest
pytest
pytest -m "not slow"  # 跳過慢速測試
pytest --cov=. --cov-report=html
```

#### 程式碼品質
```bash
# 格式化程式碼
black .
isort .

# 類型檢查
mypy core/ utils/ processing/

# 程式碼檢查
pylint core/ utils/ processing/
```

#### 系統執行
```bash
# 基本全景處理
python generate_pyramid.py

# 批次處理
python -m processing.batch_processor

# 系統健康檢查
python -c "from utils.processing_checker import check_system; check_system()"
```

## 架構總覽

### 核心系統架構

本系統透過多階段管線處理全景影像：

1. **輸入處理**：分析與載入 360° 全景影像
2. **座標轉換**：在球面與立方體座標系統間轉換
3. **立方體映射**：從全景影像生成 6 面立方體表示
4. **AI 檢測**：使用 YOLO 進行人臉/車牌檢測
5. **隱私保護**：對敏感區域套用模糊/馬賽克效果
6. **輸出生成**：建立處理後的影像與網路可視金字塔

### 關鍵元件

#### **核心演算法 (`core/`)**
- **`cube_mapping.py`**：處理立方體面生成，支援多種格式（horizon、list、dict、dice）
- **`coordinate/`**：模組化座標轉換系統，具備快取功能
- **`interpolation/`**：高效能影像插值演算法
- **`projection/`**：球面到立方體投影轉換
- **`samplers/`**：影像處理的最佳化取樣策略

#### **處理管線 (`processing/`)**
- **`panorama_processor.py`**：從舊版 main.py 演化的主要處理協調器
- **`batch_processor.py`**：處理基於目錄的批次處理
- **`pyramid_generator.py`**：建立多解析度影像金字塔
- **`pyramid_config.py`**：金字塔生成的配置管理

#### **AI 檢測 (`detection/`)**
- **`detector.py`**：基於 YOLO 的人臉與車牌檢測
- **`models/`**：預訓練模型定義與配置
- **`strategies/`**：檢測策略實作
- **`postprocessing/`**：檢測結果處理與過濾

#### **統一管理系統 (`utils/`)**
- **`unified_memory_manager.py`**：記憶體分配與最佳化
- **`unified_performance_monitor.py`**：系統資源監控
- **`unified_distributed_processor.py`**：分散式處理協調
- **`gpu_manager.py`**：GPU 裝置管理與最佳化

### 處理模式

系統透過 `config.constants` 支援多種處理模式：

- **`SaveMode.ALL`**：儲存所有中間結果
- **`SaveMode.CUBE_ONLY`**：僅儲存立方體面
- **`SaveMode.BLUR_ONLY`**：僅儲存隱私保護版本
- **`ProcessMode.SINGLE`**：處理單一影像
- **`ProcessMode.BATCH`**：處理整個目錄

## 編碼標準

### 程式碼風格指南
- **變數/函數**：`snake_case`（例如：`process_image`、`file_path`）
- **類別**：`PascalCase`（例如：`PanoramaProcessor`、`ImageAnalyzer`）
- **常數**：`UPPER_SNAKE_CASE`（例如：`MAX_IMAGE_SIZE`、`DEFAULT_CUBE_SIZE`）
- **檔案**：`snake_case.py`（例如：`panorama_processor.py`）

### 類型提示 (Python 3.10+)
使用現代類型提示而非舊版 `typing` 模組：
```python
# ✅ 正確用法
def process_image(image_path: str, size: int = 2048) -> dict[str, str | None]:
    """處理影像並回傳結果"""
    return {"status": "success", "output": None}

# ❌ 避免（舊版 typing）
from typing import Dict, Union, Optional
def process_image(image_path: str) -> Dict[str, Union[str, None]]:
    pass
```

### 匯入組織
```python
# 1. 標準函式庫
import os
import sys
from pathlib import Path

# 2. 第三方函式庫
import numpy as np
import cv2

# 3. 本地模組
from core.coordinate import CoordinateTransformer
from utils.image_utils import load_image
```

## 使用範例

### 基本處理
```python
from config.settings import get_config
from processing.panorama_processor import PanoramaProcessor
from detection.detector import Detector

# 初始化元件
config = get_config()
detector = Detector(model_path="models/yolo_face_plate.pt")
processor = PanoramaProcessor(detector=detector)

# 處理單一影像
result = processor.process_panorama("input.jpg", "output/")
print(f"處理完成：{result}")
```

### 批次處理
```python
from processing.batch_processor import BatchProcessor

batch_processor = BatchProcessor()
batch_processor.process_folder("input_folder/", "output_folder/")
```

### 記憶體管理
```python
from utils.unified_memory_manager import MemoryManager

# 智慧記憶體分配
memory_manager = MemoryManager()
with memory_manager.smart_allocation() as allocator:
    result = process_large_image(image_data)
```

## 配置管理

### 關鍵配置檔案
- **`config/settings.py`**：使用資料類別的主要配置管理
- **`config/constants.py`**：系統常數與列舉
- **`config/detection_classes.txt`**：AI 檢測類別定義
- **`example_config.yaml`**：範例配置檔案

### 配置結構
```python
from config.settings import get_config, Config, ProcessingMode

# 載入預設配置
config = get_config()

# 自訂配置
config.processing.cube_size = 2048      # 設定立方體面大小
config.model.conf_threshold = 0.5       # 設定檢測信心閾值
config.system.max_workers = 4           # 設定工作程序數
config.system.enable_gpu = True         # 啟用 GPU 加速
```

## 效能最佳化

### 記憶體管理策略
系統使用統一記憶體管理器搭配不同策略：
- **`MemoryStrategy.CONSERVATIVE`**：最小記憶體使用量
- **`MemoryStrategy.BALANCED`**：效能與記憶體間的平衡
- **`MemoryStrategy.AGGRESSIVE`**：最大效能
- **`MemoryStrategy.SMART`**：AI 驅動最佳化
- **`MemoryStrategy.STREAMING`**：大檔案處理

### GPU 加速
```python
from utils.gpu_manager import get_gpu_manager

# 檢查 GPU 可用性
gpu_manager = get_gpu_manager()
if gpu_manager.cuda_available:
    print(f"GPU 可用：{gpu_manager.get_device_stats()}")
```

## 測試框架

專案使用 pytest 搭配完整測試覆蓋率：

### 測試結構
- **`test/conftest.py`**：共用測試設備與配置
- **`test/test_core/`**：核心演算法測試
- **`test/test_detection/`**：AI 檢測測試
- **`test/test_processing/`**：影像處理測試
- **`test/test_utils/`**：輔助函數測試

### 測試類別
- **單元測試**：個別函數測試
- **整合測試**：元件互動測試
- **效能測試**：記憶體與時間驗證
- **GPU 測試**：GPU 特定功能（當可用時）

## 常見問題與解決方案

### 記憶體問題
```python
# 對大檔案使用串流策略
from utils import get_memory_manager, MemoryStrategy

memory_manager = get_memory_manager()
memory_manager.strategy = MemoryStrategy.STREAMING
```

### 效能問題
```python
# 啟用效能監控
from utils import get_performance_monitor

monitor = get_performance_monitor()
monitor.start_monitoring()
```

### 模型載入問題
```bash
# 確保模型目錄存在
mkdir -p models
# YOLO 模型將在首次使用時自動下載
```

## 開發指導方針

### 程式碼相容性問題
程式碼庫正在轉換至現代 Python 標準：

1. **類型提示**：從 `typing.Union, List, Dict` 更新為 `|, list, dict`
2. **匯入排序**：使用 `isort` 自動組織匯入
3. **文件**：確保所有函數都有完整的文件字串

### 效能考量
- 對計算密集型函數使用 Numba JIT 最佳化
- 對頻繁呼叫的函數實作 `@lru_cache` 快取
- 考慮對大批次處理使用 GPU 加速
- 使用統一記憶體管理器監控記憶體使用

---

**企業級 360° 全景影像處理系統**