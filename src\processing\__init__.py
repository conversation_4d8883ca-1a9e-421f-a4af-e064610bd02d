"""
Processing Module - Enterprise-grade Image Processing Engine

This module contains complete image processing workflows, from input analysis to output
generation, supporting batch processing, progress management, HTML5 processing and other
enterprise-level functions.

Main components:
- factory: ProcessingFactory for dependency injection architecture
- pipeline: ProcessingPipeline for coordinated processing steps
- panorama_processor: Panoramic image processor (refactored coordinator)
- batch_processor: Batch processor (large-scale processing)
- scene_processor: Scene processor (unified processing interface)
- progress_manager: Progress manager (interruption recovery)
- input_analyzer: Input analyzer (intelligent directory scanning)
- pyramid_reporter: Report generator (multi-format reports)
- compatibility: Backward compatibility layer for legacy APIs
- components/: Service components (DetectionService, CubeService, etc.)
"""

# 為 PyInstaller 提供明確的套件標識
__package__ = "processing"
__version__ = "1.0.0"

# 處理模組和主要類別列表
__all__ = [
    # 子模組
    "batch_processor",
    "progress_manager", 
    "input_analyzer",
    "scene_processor",
    "pyramid_reporter",
    "panorama_processor",
    "pyramid_generator",
    "pyramid_config",
    "factory",
    "pipeline",
    "compatibility",
    "components",
    # 主要類別 (將在下方動態添加)
]

# 確保此模組被正確識別為套件
def _ensure_package():
    """Ensure this module is correctly identified as a package"""
    return True

_package_initialized = _ensure_package()

# 嘗試導入主要組件
try:
    # 從各個模組導入主要類別
    from .batch_processor import AdvancedBatchProcessor
    from .input_analyzer import InputAnalyzer
    from .panorama_processor import PanoramaProcessor
    from .progress_manager import ProgressManager, TemporalProgressManager
    from .pyramid_reporter import PyramidReporter, ProcessingStats, SceneReport
    from .scene_processor import SceneProcessor
    from .pyramid_generator import PyramidGenerator, PyramidTileGenerator
    from .pyramid_config import PyramidConfig, PyramidQuality, TileFormat
    from .factory import ProcessingFactory
    from .pipeline import (
        ProcessingPipeline,
        ProcessingContext,
        ProcessingResult,
        ProcessingStep,
        LoadImageStep,
        ConvertToCubeStep,
        DetectObjectsStep,
        ApplyBlurStep,
        SaveResultsStep,
    )
    from .compatibility import LegacyPanoramaProcessor, LegacyBatchProcessor

    # 將主要類別添加到 __all__
    __all__.extend([
        # 主要處理器
        "AdvancedBatchProcessor",
        "PanoramaProcessor", 
        "SceneProcessor",
        "InputAnalyzer",
        # 進度管理
        "ProgressManager",
        "TemporalProgressManager",
        # 金字塔相關
        "PyramidGenerator",
        "PyramidTileGenerator",
        "PyramidReporter",
        "PyramidConfig",
        "PyramidQuality",
        "TileFormat",
        "ProcessingStats",
        "SceneReport",
        # 工廠和管線
        "ProcessingFactory",
        "ProcessingPipeline",
        "ProcessingContext", 
        "ProcessingResult",
        "ProcessingStep",
        # 管線步驟
        "LoadImageStep",
        "ConvertToCubeStep",
        "DetectObjectsStep",
        "ApplyBlurStep", 
        "SaveResultsStep",
        # 向後兼容
        "LegacyPanoramaProcessor",
        "LegacyBatchProcessor",
    ])

    # 子模組導入
    from . import (
        batch_processor,
        input_analyzer,
        panorama_processor,
        progress_manager,
        pyramid_reporter,
        pyramid_generator,
        pyramid_config,
        scene_processor,
        factory,
        pipeline,
        compatibility,
        components,
    )

except ImportError as e:
    import sys
    import warnings

    warnings.warn(f"Processing模組部分組件導入失敗: {e}", ImportWarning)
    
    # 嘗試僅導入基本功能
    try:
        from .panorama_processor import PanoramaProcessor
        from .batch_processor import AdvancedBatchProcessor
        __all__ = ["PanoramaProcessor", "AdvancedBatchProcessor"]
    except ImportError:
        __all__ = []
        if hasattr(sys, "_MEIPASS"):  # PyInstaller 環境
            print(f"Processing module import warning (PyInstaller environment): {e}")
        else:
            print(f"Processing module import warning: {e}")
