"""
Log Utils 模組 v2.1 - 企業級模組化日誌系統

本 `__init__.py` 檔案是 `log_utils` 模組的總入口，負責定義其公開 API，
並以一種清晰、模組化的方式組織其所有功能。

## 設計哲學

- **易用性優先**: 透過 `factory.py` 提供高階便利函式，讓開發者能用最少的程式碼快速設定功能完備的日誌器。
- **深度可自訂**: 對於進階使用者，可以透過 `core.config` 和 `core.manager` 來深度自訂日誌系統的每一個環節。
- **模組化與可擴展**: 將不同功能（設定、管理、格式化、處理）分離到獨立的模組中，使得系統易於維護且便於未來擴展新功能。
- **穩健性**: 透過優雅的降級處理，即使在缺少某些選用相依性的環境中，也能保證核心功能可用。

## 建議使用模式

```python
# 1. 從 factory 匯入高階函式
from .factory import create_logger_from_preset, get_logger
import logging

# 2. 使用預設組態快速建立日誌器
#    這是最推薦的方式，能滿足 90% 的需求。
dev_logger = create_logger_from_preset("my_module", preset="development")
dev_logger.info("開發模式啟動...")

# 3. 在專案的任何地方，透過名稱獲取已設定好的日誌器
#    這確保了在整個應用程式中使用的是同一個 logger 實例。
module_logger = get_logger("my_module")
module_logger.debug("這是一條來自同一個 logger 的偵錯訊息。")

# 4. 直接使用從 logging 重新匯出的級別常數
dev_logger.log(logging.WARNING, "這是一條警告訊息。")
```
"""

# --- 套件元資料 ---
__package__ = "log_utils"
__version__ = "2.1.0"
__author__ = "AI 部門 - 全景處理團隊"
__docformat__ = "restructuredtext"

# --- 標準庫匯入與重新匯出 ---
# 為了方便使用者，我們直接從這裡重新匯出 logging 的核心元件。
# 使用者因此可以 `from log_utils import DEBUG` 而非 `from logging import DEBUG`。
import logging
import sys

getLogger = logging.getLogger
basicConfig = logging.basicConfig
DEBUG = logging.DEBUG
INFO = logging.INFO
WARNING = logging.WARNING
ERROR = logging.ERROR
CRITICAL = logging.CRITICAL

# --- 相依性檢查 ---
# 優雅地處理 `logging.handlers` 這個在某些精簡 Python 環境中可能不存在的模組。
try:
    import logging.handlers
    HAS_HANDLERS = True
except ImportError:
    HAS_HANDLERS = False
    import warnings
    warnings.warn(
        "標準庫 `logging.handlers` 不可用，檔案輪轉 (file rotation) 等進階日誌處理功能將受限。",
        ImportWarning
    )

# --- 模組化 API 匯出 ---
# 這是模組的核心，定義了 `from log_utils import *` 時會匯出的公開介面。
# 使用 try-except 結構確保即使在匯入失敗時，程式也能以最基本的功能降級執行。
try:
    # 從核心模組匯入
    from .core.config import LogConfig, create_config, LogConfigPresets
    from .core.manager import LogManager

    # 從工廠模組匯入最常用的高階 API
    from .factory import (
        setup_logger,
        setup_basic_logger,
        setup_simple_logger,
        create_tool_logger,
        create_logger_from_preset,
        get_logger,
        get_global_log_manager,
        set_logger_level,
        list_loggers,
        get_logging_statistics,
        shutdown_logging,
        cleanup_old_logs,
    )

    # 從格式器模組匯入
    from .formatters import (
        ColoredFormatter,
        JSONFormatter,
        CompactFormatter,
        DetailedFormatter,
        create_formatter,
    )

    # 從處理器模組匯入
    from .handlers import (
        create_console_handler,
        create_file_handler,
        create_handler,
        create_handler_from_preset,
    )

    # `__all__` 列表是 Python 的一個慣例，用來定義模組的「公開 API」。
    # 它明確地列出了可以被外部使用的名稱，有助於靜態分析和程式碼的可讀性。
    __all__ = [
        # -- logging 標準庫重新匯出 --
        "getLogger", "basicConfig", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL",
        
        # -- 核心元件 --
        "LogManager", "LogConfig", "create_config", "LogConfigPresets",
        
        # -- 高階工廠函式 (最常用) --
        "setup_logger", "get_logger", "create_logger_from_preset",
        
        # -- 向後相容的工廠函式 --
        "setup_basic_logger", "setup_simple_logger", "create_tool_logger",
        
        # -- 管理函式 --
        "get_global_log_manager", "set_logger_level", "list_loggers",
        "get_logging_statistics", "shutdown_logging", "cleanup_old_logs",

        # -- 格式器相關 --
        "ColoredFormatter", "JSONFormatter", "CompactFormatter", "DetailedFormatter", "create_formatter",
        
        # -- 處理器相關 --
        "create_console_handler", "create_file_handler", "create_handler", "create_handler_from_preset",
        
        # -- 狀態旗標 --
        "HAS_HANDLERS",
    ]

except ImportError as e:
    # --- 穩健的降級處理 ---
    # 如果上述任何一個匯入失敗，發出警告並只匯出最基本的功能。
    import warnings
    warnings.warn(f"部分 `log_utils` 功能因匯入錯誤而無法使用: {e}", ImportWarning)

    # 在降級模式下，只提供最核心、無相依性的 logging 元件
    __all__ = [
        "getLogger", "basicConfig", "DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL", "HAS_HANDLERS",
    ]


# --- 模組輔助函式 ---

def get_module_info() -> dict[str, str]:
    """
    取得關於本模組的詳細架構資訊。

    :return: 包含模組元資料的字典。
    """
    return {
        "version": __version__,
        "author": __author__,
        "architecture": "模組化 (核心、工廠、格式器、處理器)",
        "design_patterns": "工廠模式 (Factory), 單例模式 (Singleton)",
        "has_handlers_support": str(HAS_HANDLERS),
        "key_features": "可設定的預設組、多樣化的格式器、可插拔的處理器、全域管理",
        "python_version": f"{sys.version_info.major}.{sys.version_info.minor}",
    }


def check_compatibility() -> bool:
    """
    執行一個簡單的相容性檢查。

    :return: 如果基本相依性滿足，則回傳 True。
    """
    try:
        import logging, os, threading
        return True
    except ImportError as e:
        warnings.warn(f"相容性檢查失敗: {e}", ImportWarning)
        return False

# 在模組載入時執行一次相容性檢查
_compatibility_ok = check_compatibility()
