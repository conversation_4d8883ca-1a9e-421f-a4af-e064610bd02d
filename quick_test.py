#!/usr/bin/env python3
"""
快速導入測試
"""

def quick_test():
    print("Testing basic imports...")
    
    try:
        import config
        print("[OK] config imported")
    except Exception as e:
        print(f"[FAIL] config: {e}")
    
    try:
        import core
        print("[OK] core imported")
    except Exception as e:
        print(f"[FAIL] core: {e}")
    
    try:
        import detection
        print("[OK] detection imported")
    except Exception as e:
        print(f"[FAIL] detection: {e}")
    
    try:
        import log_utils
        print("[OK] log_utils imported")
    except Exception as e:
        print(f"[FAIL] log_utils: {e}")
    
    try:
        import processing
        print("[OK] processing imported")
    except Exception as e:
        print(f"[FAIL] processing: {e}")
    
    try:
        import utils
        print("[OK] utils imported")
    except Exception as e:
        print(f"[FAIL] utils: {e}")

if __name__ == "__main__":
    quick_test()