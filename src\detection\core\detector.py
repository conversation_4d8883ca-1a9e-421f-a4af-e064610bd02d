"""
重構後的偵測器類別

現代化的模組化偵測器，使用策略模式和可組合的後處理流程。
專注於協調與編排，而非直接的實作。
"""

import time
from typing import Any

import cv2
import numpy as np
import torch

from log_utils.factory import get_logger

from ..models.manager import ModelManager
from ..postprocessing.filters import CenterRegionFilter, LargeBoxFilter
from ..postprocessing.merger import MergerProcessor
from ..postprocessing.pipeline import PostProcessingPipeline
from ..strategies.factory import StrategyFactory
from ..utils.blur import BlurProcessor
from ..utils.statistics import StatisticsCollector
from .config import DetectionConfig
from .data_structures import DetectionBox

# 可選的進階元件
try:
    from utils.gpu_manager import DeviceType, get_gpu_manager

    HAS_GPU_MANAGER = True
except ImportError:
    HAS_GPU_MANAGER = False

try:
    from utils.unified_memory_manager import MemoryStrategy, create_memory_manager

    HAS_UNIFIED_MEMORY = True
except ImportError:
    HAS_UNIFIED_MEMORY = False

try:
    from utils.unified_performance_monitor import get_performance_monitor

    HAS_PERFORMANCE_MONITOR = True
except ImportError:
    HAS_PERFORMANCE_MONITOR = False

logger = get_logger(__name__)


class Detector:
    """
    採用模組化架構的重構偵測器

    使用策略模式處理偵測邏輯，並採用可組合的後處理流程。
    專注於協調而非直接的實作。
    """

    def __init__(self, config: DetectionConfig):
        """初始化模組化偵測器

        :param config: 偵測設定
        """
        self.config = config

        # 驗證設定
        config_issues = config.validate()
        if config_issues:
            raise ValueError(f"無效的設定: {', '.join(config_issues)}")

        # 初始化元件
        self.model_manager = ModelManager()
        self.strategy_factory = StrategyFactory()
        self.blur_processor = BlurProcessor()
        self.statistics_collector = StatisticsCollector()

        # 初始化進階元件 (可選)
        self._init_advanced_components()

        # 設定裝置與模型
        self._setup_device()
        self._setup_models()

        # 建立後處理流程
        self.pipeline = self._create_pipeline()

        logger.info("模組化偵測器初始化成功")

    def _init_advanced_components(self):
        """初始化可選的進階元件"""
        self.memory_manager = None
        self.performance_monitor = None

        # 統一記憶體管理器
        if HAS_UNIFIED_MEMORY:
            try:
                self.memory_manager = create_memory_manager(
                    strategy=MemoryStrategy.BALANCED
                )
                logger.info("統一記憶體管理器已啟用")
            except Exception as e:
                logger.warning(f"統一記憶體管理器初始化失敗: {e}")

        # 效能監視器
        if HAS_PERFORMANCE_MONITOR:
            try:
                self.performance_monitor = get_performance_monitor()
                self.performance_monitor.start_monitoring()
                logger.info("效能監視器已啟用")
            except Exception as e:
                logger.warning(f"效能監視器初始化失敗: {e}")

    def _setup_device(self):
        """使用 GPU 管理器優化設定偵測裝置"""
        if self.config.device is None:
            if HAS_GPU_MANAGER:
                try:
                    gpu_manager = get_gpu_manager()
                    best_device = gpu_manager.get_best_device(
                        preferred_type=DeviceType.CUDA
                    )
                    if best_device:
                        device = f"cuda:{best_device.device_id}"
                        logger.info(
                            f"GPU 管理器已選擇: {best_device.name} ({best_device.total_memory}MB)"
                        )
                    else:
                        device = "cpu"
                        logger.info("GPU 管理器: 無 CUDA 裝置，使用 CPU")
                except Exception as e:
                    logger.warning(
                        f"GPU 管理器失敗: {e}，退回基本偵測"
                    )
                    device = "cuda" if torch.cuda.is_available() else "cpu"
            else:
                device = "cuda" if torch.cuda.is_available() else "cpu"
        else:
            device = self._normalize_device_string(self.config.device)

        self.device = device
        self.model_manager.set_device(device)
        logger.info(f"偵測裝置: {device}")

    def _normalize_device_string(self, device_str: str) -> str:
        """為 PyTorch 相容性正規化裝置字串

        :param device_str: 原始裝置字串
        :return: 正規化後的裝置字串
        """
        device_str = device_str.lower().strip()

        # 處理 GPU 裝置名稱
        if any(
            keyword in device_str for keyword in ["nvidia", "geforce", "rtx", "gtx"]
        ):
            if torch.cuda.is_available():
                logger.info(f"偵測到 GPU 裝置名稱 '{device_str}'，使用 'cuda'")
                return "cuda"
            else:
                logger.warning(f"CUDA 裝置 '{device_str}' 不可用，使用 CPU")
                return "cpu"

        # 處理標準格式
        if device_str in ["cpu", "cuda"] or device_str.startswith("cuda:"):
            return device_str

        # 處理 cuda 關鍵字
        if "cuda" in device_str:
            return "cuda" if torch.cuda.is_available() else "cpu"

        # 預設為 CPU
        logger.warning(f"無法辨識的裝置字串 '{device_str}'，使用 CPU")
        return "cpu"

    def _setup_models(self):
        """使用模型管理器設定偵測模型"""
        try:
            self.model_manager.setup_standard_models(
                self.config.primary_model_path, self.config.secondary_model_path
            )

            # 驗證模型
            validation_results = self.model_manager.validate_models()
            failed_models = [
                model_id for model_id, valid in validation_results.items() if not valid
            ]

            if failed_models:
                logger.warning(f"模型驗證失敗: {failed_models}")

            logger.info(
                f"模型設定完成: 已載入 {len(validation_results)} 個模型"
            )

        except Exception as e:
            logger.error(f"模型設定失敗: {e}")
            raise

    def _create_pipeline(self) -> PostProcessingPipeline:
        """根據設定建立後處理流程

        :return: 已設定的流程
        """
        pipeline = PostProcessingPipeline()

        # 標準流程: 合併 -> 過濾大型 -> (條件式中央過濾)
        pipeline.add_processor(MergerProcessor(self.config.iou_threshold))
        pipeline.add_processor(LargeBoxFilter(self.config.max_area_ratio))
        pipeline.add_processor(CenterRegionFilter())  # 僅適用於面 5

        logger.debug(f"已建立流程: {pipeline}")
        return pipeline

    def detect(self, image: np.ndarray, face_id: int) -> dict[str, Any]:
        """在單一面上執行偵測

        :param image: 輸入影像
        :param face_id: 立方體面 ID (0-5)
        :return: 偵測結果字典
        """
        start_time = time.time()

        try:
            # 1. 選擇並執行偵測策略
            strategy = self.strategy_factory.get_strategy(face_id)
            detections = strategy.detect(
                image, self.model_manager.get_models(), self.config
            )

            # 2. 應用後處理流程
            context = {
                "face_id": face_id,
                "image_shape": image.shape[:2],
                "iou_threshold": self.config.iou_threshold,
                "max_area_ratio": self.config.max_area_ratio,
            }

            processed_detections = self.pipeline.process(detections, context)

            # 3. 應用模糊處理
            blur_regions = self.blur_processor.process_detections(
                image, processed_detections, self.config.enable_draw
            )

            # 4. 記錄統計數據
            processing_time = time.time() - start_time
            self.statistics_collector.record_detection(
                face_id, len(processed_detections), processing_time
            )

            # 5. 記錄效能指標
            if self.performance_monitor:
                self.performance_monitor.record_operation(
                    f"detection_face_{face_id}", processing_time
                )

            return blur_regions

        except Exception as e:
            logger.error(f"面 {face_id} 的偵測失敗: {e}")
            return {}

    def batch_process_faces(
        self, face_images: dict[int, np.ndarray]
    ) -> dict[int, dict[str, dict]]:
        """批次處理多個立方體面

        :param face_images: face_id -> image 的字典
        :return: face_id -> blur_regions 的字典
        """
        logger.info(f"開始為 {len(face_images)} 個面進行批次偵測")
        start_time = time.time()

        results = {}

        for face_id, image in face_images.items():
            try:
                blur_regions = self.detect(image, face_id)
                results[face_id] = blur_regions

                logger.debug(
                    f"面 {face_id} 已處理: {len(blur_regions)} 個模糊區域"
                )

            except Exception as e:
                logger.error(f"處理面 {face_id} 時發生錯誤: {e}")
                results[face_id] = {}

        # 整體統計
        processing_time = time.time() - start_time
        total_regions = sum(len(regions) for regions in results.values())

        self.statistics_collector.record_batch(
            len(face_images), total_regions, processing_time
        )

        logger.info(
            f"批次處理完成: 在 {processing_time:.2f}s 內共 {total_regions} 個區域"
        )

        return results

    def get_detection_statistics(
        self, batch_results: dict[int, dict[str, dict]]
    ) -> dict[str, Any]:
        """從批次結果中取得偵測統計數據

        :param batch_results: 批次處理結果
        :return: 統計字典
        """
        return self.statistics_collector.analyze_batch_results(batch_results)

    def get_system_statistics(self) -> dict[str, Any]:
        """取得整體系統統計數據

        :return: 系統統計數據
        """
        stats = {
            "detector_stats": self.statistics_collector.get_statistics(),
            "pipeline_stats": self.pipeline.get_statistics(),
            "model_stats": self.model_manager.get_model_summary(),
            "strategy_mapping": self.strategy_factory.get_face_strategy_mapping(),
        }

        if self.performance_monitor:
            stats["performance_stats"] = self.performance_monitor.get_summary()

        return stats

    def reset_cache(self):
        """重設快取以進行記憶體優化"""
        try:
            # 模型管理器快取
            self.model_manager.reset_cache()

            # 統一記憶體管理器快取
            if self.memory_manager:
                self.memory_manager.cleanup()

            # 統計重設
            self.statistics_collector.reset_statistics()
            self.pipeline.reset_statistics()

            logger.debug("偵測器快取已重設")

        except Exception as e:
            logger.debug(f"快取重設錯誤: {e}")

    def cleanup(self):
        """清理所有資源"""
        logger.info("正在清理偵測器資源")

        try:
            # 清理模型管理器
            self.model_manager.cleanup()

            # 清理進階元件
            if self.memory_manager:
                self.memory_manager.cleanup()
                self.memory_manager = None

            if self.performance_monitor:
                self.performance_monitor.stop_monitoring()
                self.performance_monitor = None

            # 強制進行垃圾回收
            import gc

            collected = gc.collect()
            if collected > 0:
                logger.debug(f"垃圾回收: 回收了 {collected} 個物件")

            logger.info("偵測器清理完成")

        except Exception as e:
            logger.debug(f"偵測器清理錯誤: {e}")

    def __del__(self):
        """解構子 - 清理資源"""
        try:
            self.cleanup()
        except Exception:
            pass
