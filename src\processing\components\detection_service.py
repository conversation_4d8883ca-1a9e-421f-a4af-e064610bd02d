#!/usr/bin/env python3
"""
統一檢測服務模組
Unified Detection Service Module

提供標準化的檢測服務接口，整合主、副檢測器，並提供緩存和雙模型協同策略。
Provides a standardized detection service interface, integrating primary and secondary detectors, and offering caching and dual-model coordination strategies.

Author: Claude Code Assistant
Date: 2025-01-19
"""

# 1. 標準庫
import hashlib
import logging
from typing import Any

# 2. 第三方庫
import numpy as np

# 3. 本地模組
try:
    from detection.core.detector import Detector
    from detection.core.data_structures import DetectionBox
    from .image_cache import ImageCache
    HAS_DETECTION = True
except ImportError:
    HAS_DETECTION = False
    # 佔位符
    class Detector: pass
    class DetectionBox: pass
    class ImageCache: pass

class DetectionService:
    """
    統一的檢測服務，專注於物體檢測。

    職責:
    - 協調主、副檢測器。
    - 提供統一的批次處理接口。
    - 實現檢測結果的緩存。
    - 應用雙模型檢測策略（例如，合併結果、置信度過濾）。
    """
    
    def __init__(self, primary_detector: Detector, secondary_detector: Detector | None = None, cache: ImageCache | None = None):
        """
        初始化檢測服務
        
        Args:
            primary_detector: 主檢測器實例
            secondary_detector: 副檢測器實例 (可選)
            cache: 圖像和數據緩存實例 (可選)
        """
        if not HAS_DETECTION:
            raise ImportError("Detection module is not available. Cannot initialize DetectionService.")
            
        self.primary_detector = primary_detector
        self.secondary_detector = secondary_detector
        self.cache = cache if cache else ImageCache(max_size_mb=256) # 如果未提供，則使用內部緩存
        self.logger = logging.getLogger(__name__)

    def _generate_cache_key(self, image: np.ndarray, face_id: int) -> str:
        """生成緩存鍵"""
        image_hash = hashlib.sha256(image.tobytes()).hexdigest()
        return f"detection_{image_hash}_{face_id}"

    def detect_in_face(self, image: np.ndarray, face_id: int) -> list[DetectionBox]:
        """
        對單個立方體面進行檢測。
        
        Args:
            image: 立方體面圖像 (np.ndarray)
            face_id: 面ID
        
        Returns:
            檢測到的區域列表 (list[DetectionBox])
        """
        cache_key = self._generate_cache_key(image, face_id)
        cached_result = self.cache.get(cache_key)
        if cached_result is not None:
            self.logger.debug(f"從緩存中獲取面 {face_id} 的檢測結果")
            return cached_result

        all_regions: list[DetectionBox] = []

        # 主檢測器處理
        if self.primary_detector:
            try:
                # 假設檢測器有一個 'detect' 方法
                primary_regions = self.primary_detector.detect(image)
                all_regions.extend(primary_regions)
            except Exception as e:
                self.logger.warning(f"主檢測器在處理面 {face_id} 時失敗: {e}")

        # 副檢測器處理
        if self.secondary_detector:
            try:
                secondary_regions = self.secondary_detector.detect(image)
                all_regions.extend(secondary_regions)
            except Exception as e:
                self.logger.warning(f"副檢測器在處理面 {face_id} 時失敗: {e}")

        # TODO: 合併重疊區域
        final_regions = self._merge_overlapping_regions(all_regions)

        # 存入緩存
        self.cache.put(cache_key, final_regions, size_mb=0.1) # 假設區域列表大小約為 0.1MB
        
        return final_regions
        
    def _merge_overlapping_regions(self, regions: list[DetectionBox]) -> list[DetectionBox]:
        """合併重疊的檢測框（非極大值抑制的簡化版本）"""
        # 此處應實現一個健壯的合併邏輯，目前僅返回原始區域
        return regions

    def batch_process_faces(self, faces: dict[int, np.ndarray]) -> dict[int, list[DetectionBox]]:
        """
        批次處理多個立方體面
        
        Args:
            faces: 一個字典，鍵為 face_id，值為圖像 (np.ndarray)
        
        Returns:
            一個字典，鍵為 face_id，值為檢測到的區域列表
        """
        results = {}
        for face_id, image in faces.items():
            results[face_id] = self.detect_in_face(image, face_id)
        return results

    def clear_cache(self):
        """清空檢測緩存"""
        self.cache.clear()
        self.logger.info("檢測服務緩存已清空")
