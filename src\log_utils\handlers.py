"""
日誌處理器工廠模組 (log_utils.handlers)

本模組提供了一系列工廠函式，用於建立和設定各種日誌處理器 (Handler)。
處理器的核心職責是將格式化後的日誌訊息「傳送」到指定的目的地，例如主控台、檔案、
遠端伺服器或電子郵件。

將處理器的建立邏輯從 `LogManager` 中分離出來，使得 `LogManager` 可以更專注於
日誌器的生命週期管理，同時讓處理器的設定更具靈活性和可擴展性。
"""

import logging
import sys
from pathlib import Path
from typing import Callable, Dict, Optional

from .formatters import ColoredFormatter, create_formatter, get_format_string

# 檢查 `logging.handlers` 是否可用，這是一個包含進階處理器的標準庫模組，
# 但在某些極度精簡的 Python 環境中可能不存在。
try:
    import logging.handlers
    HAS_HANDLERS = True
except ImportError:
    HAS_HANDLERS = False


def create_console_handler(
    level: int = logging.INFO,
    use_colors: bool = True,
    simple_format: bool = False,
    format_string: Optional[str] = None,
    theme: str = "default",
) -> logging.StreamHandler:
    """
    建立一個將日誌輸出到標準輸出 (主控台) 的處理器。

    :param level: 此處理器處理的最低日誌級別。
    :param use_colors: 是否使用 `ColoredFormatter` 進行彩色輸出。
    :param simple_format: 是否使用簡潔的日誌格式。
    :param format_string: (可選) 自訂的格式字串，若提供則會覆寫 `simple_format` 的設定。
    :param theme: (可選) `ColoredFormatter` 的顏色主題。
    :return: 一個設定好的 `logging.StreamHandler` 實例。
    """
    handler = logging.StreamHandler(sys.stdout)
    handler.setLevel(level)

    # 決定要使用的格式字串
    if format_string is None:
        fmt_name = "simple" if simple_format else "basic"
        format_string = get_format_string(fmt_name)

    # 決定要使用的格式器
    formatter: logging.Formatter
    if use_colors:
        formatter = ColoredFormatter(format_string, use_colors=True, theme=theme)
    else:
        formatter = logging.Formatter(format_string)

    handler.setFormatter(formatter)
    return handler


def create_file_handler(
    filepath: str,
    level: int = logging.DEBUG,
    format_string: Optional[str] = None,
    max_bytes: int = 10 * 1024 * 1024,  # 預設 10MB
    backup_count: int = 5,
    encoding: str = "utf-8",
    delay: bool = False,
) -> logging.Handler:
    """
    建立一個將日誌寫入檔案的處理器，並支援基於檔案大小的輪轉 (rotation)。

    如果 `logging.handlers` 可用，會使用 `RotatingFileHandler`，否則會降級為
    不支援輪轉的 `FileHandler`。

    :param filepath: 日誌檔案的完整路徑。
    :param level: 此處理器處理的最低日誌級別。
    :param format_string: (可選) 自訂的格式字串，預設使用 "detailed" 格式。
    :param max_bytes: 每個日誌檔案的大小上限（位元組）。
    :param backup_count: 輪轉時要保留的舊日誌檔案數量。
    :param encoding: 檔案的編碼格式。
    :param delay: 是否延遲開啟檔案，直到第一次寫入日誌時才開啟。
    :return: 一個設定好的檔案處理器實例。
    """
    # 確保日誌檔案所在的目錄存在
    log_dir = Path(filepath).parent
    log_dir.mkdir(parents=True, exist_ok=True)

    handler: logging.Handler
    if HAS_HANDLERS:
        handler = logging.handlers.RotatingFileHandler(
            filepath,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding=encoding,
            delay=delay,
        )
    else:
        # 如果 `logging.handlers` 不可用，則降級為基本的檔案處理器
        handler = logging.FileHandler(filepath, encoding=encoding, delay=delay)

    handler.setLevel(level)

    # 設定格式器
    if format_string is None:
        format_string = get_format_string("detailed")
    formatter = logging.Formatter(format_string)
    handler.setFormatter(formatter)

    return handler


def create_error_file_handler(
    filepath: str,
    level: int = logging.ERROR,
    format_string: Optional[str] = None,
    max_bytes: int = 10 * 1024 * 1024,
    backup_count: int = 3,
    encoding: str = "utf-8",
) -> logging.Handler:
    """
    建立一個專門用來記錄錯誤 (ERROR) 和更高層級日誌的檔案處理器。

    :param filepath: 錯誤日誌的檔案路徑。
    :param level: 最低日誌級別，預設為 `logging.ERROR`。
    :param format_string: (可選) 格式字串，預設使用最詳細的 "full" 格式。
    :param max_bytes: 檔案大小上限。
    :param backup_count: 備份數量。
    :param encoding: 檔案編碼。
    :return: 一個設定好的錯誤日誌檔案處理器。
    """
    return create_file_handler(
        filepath=filepath,
        level=level,
        format_string=format_string or get_format_string("full"),
        max_bytes=max_bytes,
        backup_count=backup_count,
        encoding=encoding,
    )


def create_debug_file_handler(
    filepath: str,
    level: int = logging.DEBUG,
    format_string: Optional[str] = None,
    max_bytes: int = 50 * 1024 * 1024, # 預設 50MB
    backup_count: int = 10,
    encoding: str = "utf-8",
) -> logging.Handler:
    """
    建立一個專門用來記錄詳細偵錯資訊的檔案處理器。

    :param filepath: 偵錯日誌的檔案路徑。
    :param level: 最低日誌級別，預設為 `logging.DEBUG`。
    :param format_string: (可選) 格式字串，預設使用包含函式名稱的 "debug" 格式。
    :param max_bytes: 檔案大小上限。
    :param backup_count: 備份數量。
    :param encoding: 檔案編碼。
    :return: 一個設定好的偵錯日誌檔案處理器。
    """
    return create_file_handler(
        filepath=filepath,
        level=level,
        format_string=format_string or get_format_string("debug"),
        max_bytes=max_bytes,
        backup_count=backup_count,
        encoding=encoding,
    )


def create_json_file_handler(
    filepath: str,
    level: int = logging.INFO,
    max_bytes: int = 50 * 1024 * 1024,
    backup_count: int = 5,
    encoding: str = "utf-8",
    include_extra: bool = True,
) -> logging.Handler:
    """
    建立一個將日誌以 JSON 格式寫入檔案的處理器。

    :param filepath: JSON 日誌的檔案路徑。
    :param level: 最低日誌級別。
    :param max_bytes: 檔案大小上限。
    :param backup_count: 備份數量。
    :param encoding: 檔案編碼。
    :param include_extra: 是否在 JSON 中包含 `extra` 字典的內容。
    :return: 一個設定好的 JSON 檔案處理器。
    """
    handler = create_file_handler(
        filepath=filepath,
        level=level,
        max_bytes=max_bytes,
        backup_count=backup_count,
        encoding=encoding,
    )
    # 將預設的 Formatter 替換為 JSONFormatter
    formatter = create_formatter("json", include_extra=include_extra, ensure_ascii=False)
    handler.setFormatter(formatter)
    return handler


def create_syslog_handler(
    address: str = "localhost",
    port: int = 514,
    facility: int = logging.handlers.SysLogHandler.LOG_USER,
    level: int = logging.INFO,
    format_string: Optional[str] = None,
) -> logging.Handler:
    """
    建立一個將日誌傳送到遠端 Syslog 伺服器的處理器。

    :param address: Syslog 伺服器的位址。
    :param port: Syslog 伺服器的連接埠。
    :param facility: Syslog 的 facility code。
    :param level: 最低日誌級別。
    :param format_string: (可選) 格式字串。
    :return: 一個設定好的 `SysLogHandler` 實例。
    :raises ImportError: 如果 `logging.handlers` 模組不可用。
    """
    if not HAS_HANDLERS:
        raise ImportError("`logging.handlers` 模組不可用，無法建立 Syslog 處理器。")

    handler = logging.handlers.SysLogHandler(address=(address, port), facility=facility)
    handler.setLevel(level)

    if format_string is None:
        format_string = "%(name)s[%(process)d]: %(levelname)s - %(message)s"
    formatter = logging.Formatter(format_string)
    handler.setFormatter(formatter)

    return handler


def create_memory_handler(
    capacity: int = 1000,
    flush_level: int = logging.ERROR,
    target_handler: Optional[logging.Handler] = None,
    level: int = logging.DEBUG,
) -> logging.Handler:
    """
    建立一個在記憶體中緩衝日誌的處理器。

    當收到的日誌達到 `flush_level` 或緩衝區滿時，它會將所有緩衝的日誌
    一次性地傳送給 `target_handler`。這對於減少 I/O 操作、提升效能很有幫助。

    :param capacity: 記憶體中最多能緩衝的日誌記錄數量。
    :param flush_level: 觸發刷新的日誌級別。
    :param target_handler: (可選) 接收緩衝日誌的目標處理器。
    :param level: 此處理器處理的最低日誌級別。
    :return: 一個設定好的 `MemoryHandler` 實例。
    :raises ImportError: 如果 `logging.handlers` 模組不可用。
    """
    if not HAS_HANDLERS:
        raise ImportError("`logging.handlers` 模組不可用，無法建立 Memory 處理器。")

    handler = logging.handlers.MemoryHandler(
        capacity=capacity, flushLevel=flush_level, target=target_handler
    )
    handler.setLevel(level)
    return handler


def create_smtp_handler(
    mailhost: str,
    fromaddr: str,
    toaddrs: list[str],
    subject: str,
    credentials: Optional[tuple[str, str]] = None,
    secure: Optional[tuple] = None,
    level: int = logging.ERROR,
    format_string: Optional[str] = None,
) -> logging.Handler:
    """
    建立一個在發生嚴重錯誤時，透過 SMTP 傳送電子郵件的處理器。

    :param mailhost: 郵件伺服器位址及連接埠，格式為 `(host, port)`。
    :param fromaddr: 發件人電子郵件地址。
    :param toaddrs: 收件人電子郵件地址列表。
    :param subject: 郵件主旨。
    :param credentials: (可選) 登入郵件伺服器的認證資訊，格式為 `(username, password)`。
    :param secure: (可選) 如果需要使用安全連線 (TLS)，提供一個空的元組 `()`。
    :param level: 最低日誌級別，通常設為 `logging.ERROR` 或 `logging.CRITICAL`。
    :param format_string: (可選) 格式字串。
    :return: 一個設定好的 `SMTPHandler` 實例。
    :raises ImportError: 如果 `logging.handlers` 模組不可用。
    """
    if not HAS_HANDLERS:
        raise ImportError("`logging.handlers` 模組不可用，無法建立 SMTP 處理器。")

    handler = logging.handlers.SMTPHandler(
        mailhost=mailhost, fromaddr=fromaddr, toaddrs=toaddrs, subject=subject,
        credentials=credentials, secure=secure,
    )
    handler.setLevel(level)

    if format_string is None:
        format_string = get_format_string("full")
    formatter = logging.Formatter(format_string)
    handler.setFormatter(formatter)

    return handler


def create_timed_rotating_handler(
    filepath: str,
    when: str = "midnight",
    interval: int = 1,
    backup_count: int = 30,
    level: int = logging.DEBUG,
    format_string: Optional[str] = None,
    encoding: str = "utf-8",
) -> logging.Handler:
    """
    建立一個基於時間進行日誌輪轉的檔案處理器。

    :param filepath: 日誌檔案的路徑。
    :param when: 輪轉的時間單位。可以是 'S' (秒), 'M' (分), 'H' (時), 'D' (天), 'W0'-'W6' (週一至週日), 或 'midnight' (每日午夜)。
    :param interval: 輪轉的間隔。
    :param backup_count: 保留的備份檔案數量。
    :param level: 最低日誌級別。
    :param format_string: (可選) 格式字串。
    :param encoding: 檔案編碼。
    :return: 一個設定好的 `TimedRotatingFileHandler` 實例。
    :raises ImportError: 如果 `logging.handlers` 模組不可用。
    """
    if not HAS_HANDLERS:
        raise ImportError("`logging.handlers` 模組不可用，無法建立時間輪轉處理器。")

    log_dir = Path(filepath).parent
    log_dir.mkdir(parents=True, exist_ok=True)

    handler = logging.handlers.TimedRotatingFileHandler(
        filepath, when=when, interval=interval, backupCount=backup_count, encoding=encoding,
    )
    handler.setLevel(level)

    if format_string is None:
        format_string = get_format_string("detailed")
    formatter = logging.Formatter(format_string)
    handler.setFormatter(formatter)

    return handler


# --- 處理器工廠 ---

def create_handler(handler_type: str, **kwargs) -> logging.Handler:
    """
    根據類型名稱建立處理器的通用工廠函式。

    :param handler_type: 處理器的類型名稱 (例如 "console", "file", "smtp")。
    :param kwargs: 傳遞給對應處理器建立函式的參數。
    :return: 一個 `logging.Handler` 的實例。
    :raises ValueError: 如果提供了未知的處理器類型。
    """
    handler_factories: Dict[str, Callable[..., logging.Handler]] = {
        "console": create_console_handler,
        "file": create_file_handler,
        "error_file": create_error_file_handler,
        "debug_file": create_debug_file_handler,
        "json_file": create_json_file_handler,
        "syslog": create_syslog_handler,
        "memory": create_memory_handler,
        "smtp": create_smtp_handler,
        "timed_rotating": create_timed_rotating_handler,
    }
    factory = handler_factories.get(handler_type)
    if factory is None:
        raise ValueError(f"未知的處理器類型: '{handler_type}'. 可用類型: {list(handler_factories.keys())}")
    
    return factory(**kwargs)


# --- 處理器預設組 ---
# 提供一系列預先定義好的處理器組態，方便快速建立。
HANDLER_PRESETS = {
    "console_simple": {
        "handler_type": "console", "level": logging.INFO, "simple_format": True, "use_colors": True,
    },
    "console_detailed": {
        "handler_type": "console", "level": logging.DEBUG, "simple_format": False, "use_colors": True,
    },
    "file_basic": {
        "handler_type": "file", "level": logging.DEBUG, "max_bytes": 10 * 1024 * 1024, "backup_count": 5,
    },
    "file_production": {
        "handler_type": "file", "level": logging.INFO, "max_bytes": 50 * 1024 * 1024, "backup_count": 10,
    },
    "error_only_file": {
        "handler_type": "error_file", "level": logging.ERROR, "max_bytes": 10 * 1024 * 1024, "backup_count": 3,
    },
}


def create_handler_from_preset(preset_name: str, **overrides) -> logging.Handler:
    """
    從預設組態建立一個處理器。

    :param preset_name: 預設組的名稱。
    :param overrides: (可選) 任何要覆寫預設組設定的參數。
    :return: 一個 `logging.Handler` 的實例。
    :raises ValueError: 如果提供了未知的預設組名稱。
    """
    if preset_name not in HANDLER_PRESETS:
        raise ValueError(f"未知的處理器預設組: '{preset_name}'. 可用預設組: {list(HANDLER_PRESETS.keys())}")

    config = HANDLER_PRESETS[preset_name].copy()
    config.update(overrides)

    handler_type = config.pop("handler_type")
    return create_handler(str(handler_type), **config)
