# Gemini 專案記憶體

## 專案概述

此專案是一個企業級的360度全景影像處理系統，專為大規模處理360度全景影像而設計。其核心功能是將全景圖轉換為立方體映射金字塔（Cube Map Pyramid），並在此過程中整合AI目標檢測（如人臉、車牌）以進行隱私保護（模糊處理）。

系統經過了多次迭代，存在一些歷史版本和重複的功能模組，目前正處於一個需要整合和重構的階段。`ANALYSIS_REPORT.md` 文件中明確指出了這些架構問題，並提出了統一化的重構建議。

## 核心處理流程

主要的處理管線如下：
`全景影像 (Panorama) -> 立方體映射 (Cube Mapping) -> AI 檢測 (Detection) -> 模糊處理 (Blurring) -> 金字塔生成 (Pyramid Generation) -> 瓦片切割 (Tiling)`

系統支援多種處理模式，由 `generate_pyramid.py` 或 `universal_panorama_tool.py` 等入口腳本根據設定檔（如 `example_config.yaml`）進行調度：
1.  **pano_to_cube**: 從全景圖開始的完整處理流程。
2.  **cube_to_cube**: 處理已存在的立方體面資料夾（通常是 `html5` 目錄）。
3.  **list_cube**: 根據提供的清單（CSV/Excel/TXT）選擇性地批次處理立方體資料夾。

## 專案架構與關鍵模組

-   **`config/`**: 系統的配置中心。
    -   `settings.py`: 定義了分層的配置類別（`Config`, `ModelConfig`, `ProcessingConfig` 等）。
    -   `constants.py`: 定義了系統中使用的各種常數，如 `Face` 枚舉、支援的圖片格式等。

-   **`core/`**: 核心算法引擎，負責數學和幾何計算。
    -   `coordinate.py`: **極其重要**。實現了球面、笛卡爾和立方體座標之間的轉換。此模組使用了 Numba JIT 加速，但 `ANALYSIS_REPORT.md` 指出其與 `generate_pyramid.py` 中的轉換邏輯存在**鏡像問題**。報告確認 `generate_pyramid.py` 中的實現是正確的。
    -   `interpolation.py`: 提供了超過38種高性能的影像插值算法，並支援GPU加速。
    -   `cube_mapping.py`: 處理立方體六個面的格式轉換（如 `horizon`, `list`, `dict`, `dice`）。
    -   `projection.py`: 實現了球面到立方體的投影變換。

-   **`detection/`**: AI 智能檢測模組。
    -   `detector.py`: 封裝了 YOLO 模型，實現了雙模型協同檢測，並針對立方體的不同面（特別是第4面天空和第5面地面）有特殊的處理策略。

-   **`processing/`**: 影像處理管線的實現。
    -   `panorama_processor.py`: 處理單張全景圖或單個立方體面資料夾的核心邏輯。
    -   `pyramid_generator.py`: 負責生成多解析度的金字塔瓦片。
    -   `batch_processor.py`: 實現了高級批次處理，支援中斷恢復。
    -   `input_analyzer.py`: 智能分析輸入目錄的結構，判斷是全景圖、立方體還是場景結構。

-   **`utils/`**: 統一的工具套件。**此目錄是重構的重點**。
    -   `unified_memory_manager.py`, `unified_performance_monitor.py`, `unified_distributed_processor.py`: 這些是根據 `ANALYSIS_REPORT.md` 的建議，將舊有的多個重複模組（如 `smart_memory_manager`, `advanced_memory_manager` 等）整合而成的統一管理器，採用了現代設計模式並保持向後相容。
    -   `gpu_manager.py`: 統一管理GPU資源。
    -   `labelme_processor.py`: 處理 LabelMe 格式的標註檔案。

-   **`log_utils/`**: 提供了統一的日誌管理系統，支援彩色輸出和檔案輪轉。

-   **`test/`**: 包含了專案的單元測試，使用 `pytest` 作為測試框架。

-   **`generate_pyramid.py` / `universal_panorama_tool.py`**: 專案的主要執行入口。`generate_pyramid.py` 是一個功能完整但可能較舊的實現，而 `universal_panorama_tool.py` 似乎是一個更統一、更現代的工具入口。

## 架構問題與重構方向 (重要)

`ANALYSIS_REPORT.md` 報告了系統存在的**嚴重架構問題**：

1.  **功能重複**:
    -   **記憶體管理**: 存在4個不同的記憶體管理器 (`unified_memory_manager.py`, `smart_memory_manager.py`, `advanced_memory_manager.py`, `memory_utils.py`)。
    -   **性能監控**: 存在3個性能監控器。
    -   **分散式處理**: 存在3個分散式處理器。
    -   **座標轉換**: 邏輯散佈在 `core` 模組和 `generate_pyramid.py` 中。

2.  **座標轉換不一致**:
    -   `core/coordinate.py` 中的實現與 `generate_pyramid.py` 中的實現存在**鏡像（mirror）問題**。
    -   **`generate_pyramid.py` 中的座標轉換邏輯被驗證為正確的標準**。在進行任何座標相關的修改時，應以此為準。

3.  **重構目標**:
    -   將所有重複的功能統一到 `utils/` 下的 `unified_*` 模組中。
    -   修復並統一座標轉換邏輯，以 `generate_pyramid.py` 的實現為基準。
    -   建立一個統一的處理引擎 `UnifiedPanoramaProcessor`，整合所有最佳實踐。

## 編碼規範

`CLAUDE.md` 和 `input.md` 文件定義了詳細的編碼規範，包括：
-   命名風格 (`snake_case` for functions/variables, `PascalCase` for classes)。
-   使用 Python 3.10+ 的型別提示語法 (`|` 取代 `Union`, `list` 取代 `List`)。
-   嚴格的 `import` 順序。
-   完整的函數文檔字符串（docstrings）。
-   使用 `pylint`, `mypy`, `black`, `isort` 進行程式碼品質管理。

在修改或添加程式碼時，我會嚴格遵守這些規範。
