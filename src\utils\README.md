# 🛠️ Utils Module - Enterprise Utility Ecosystem

> **A comprehensive, unified enterprise-grade utility ecosystem providing memory management, performance monitoring, distributed processing, and system optimization for high-performance 360° panoramic image processing.**

[![Utils Module](https://img.shields.io/badge/Utils-Enterprise-blue.svg)](https://github.com/user/repo)
[![Performance](https://img.shields.io/badge/Performance-Optimized-green.svg)](https://github.com/user/repo)
[![Memory](https://img.shields.io/badge/Memory-Smart-orange.svg)](https://github.com/user/repo)
[![Distributed](https://img.shields.io/badge/Distributed-Scalable-purple.svg)](https://github.com/user/repo)

---

## 📖 模組概述

`utils` 模組是整個系統的**基礎設施層**，提供企業級的系統資源管理、性能監控、分散式處理等核心功能。經過現代化重構，此模組採用統一設計模式，將原本分散的多個獨立工具整合為三大統一管理器，搭配依賴注入容器，實現高效能、可擴展的企業級工具生態系統。

### 核心設計理念

本模組專注於解決四大核心問題：
1. **🧠 記憶體智能管理**：動態記憶體分配、快取優化、記憶體洩漏防護
2. **📊 性能即時監控**：CPU、GPU、記憶體、磁碟等系統資源的即時監控與分析
3. **🌐 分散式處理協調**：多核心、多機器的分散式任務協調與負載平衡
4. **🔧 系統優化工具**：圖像處理、檔案操作、GPU管理等專業工具集

### 技術亮點

- **🏗️ 統一架構**: 三大統一管理器 + DI容器的現代化架構
- **⚡ 智能優化**: 自適應記憶體策略、動態性能調優
- **🔄 向後兼容**: 完整的舊版API兼容層，無縫升級
- **📈 企業級監控**: 多層級監控系統，支援實時警報
- **🌍 分散式支援**: 多後端分散式處理架構

---

## 🏛️ 檔案結構

```
utils/
├── __init__.py                          # 統一介面與依賴注入容器
├── README.md                            # 本文檔
│
├── containers.py                        # 🏗️ DI容器與服務管理
│
├── unified_memory_manager.py            # 🧠 統一記憶體管理器
├── unified_performance_monitor.py       # 📊 統一性能監控器  
├── unified_distributed_processor.py     # 🌐 統一分散式處理器
│
├── core/                               # 🔧 核心工具組件
│   ├── __init__.py                     # - 核心組件介面
│   ├── config.py                       # - 工具配置管理
│   ├── alert_system.py                 # - 智能警報系統
│   ├── cache_manager.py                # - 快取管理器
│   ├── metric_collector.py             # - 指標收集器
│   ├── path_scanner.py                 # - 路徑掃描器
│   └── thresholds.py                   # - 閾值管理器
│
├── providers/                          # 🎯 服務提供者
│   ├── __init__.py                     # - 提供者介面
│   ├── memory_providers.py             # - 記憶體服務提供者
│   ├── performance_providers.py        # - 性能監控提供者
│   └── processing_providers.py         # - 處理服務提供者
│
├── tests/                              # 🧪 測試套件
│   └── __init__.py                     # - 測試框架
│
├── annotation_visualizer.py            # 🎨 標註視覺化工具
├── exe_compatibility.py                # 📦 可執行檔兼容性
├── file_utils.py                       # 📁 檔案操作工具
├── font_config.py                      # 🔤 字體配置管理
├── gpu_manager.py                      # 🎮 GPU設備管理
├── image_utils.py                      # 🖼️ 圖像處理工具
├── import_helper.py                    # 📥 導入輔助工具
├── labelme_processor.py                # 🏷️ LabelMe標註處理
├── memory_utils.py                     # 💾 記憶體輔助工具
├── processing_checker.py               # ✅ 處理檢查器
└── smart_load_balancer.py              # ⚖️ 智能負載平衡器
```

---

## 🧩 核心組件詳解

### 1. 🏗️ Dependency Injection Container (依賴注入容器)

**職責**: 統一管理所有工具組件的生命週期和依賴關係

#### 主要類別
- **`ManagerContainer`**: 統一的DI容器，管理三大統一管理器

#### 核心功能
```python
from utils import container, get_memory_manager, get_performance_monitor

# 通過DI容器獲取服務
memory_manager = container.memory_manager()
performance_monitor = container.performance_monitor()
distributed_processor = container.distributed_processor()

# 或使用便捷函數
memory_manager = get_memory_manager()
performance_monitor = get_performance_monitor()
```

#### 技術特點
- **🔧 依賴注入**: 自動解析和注入組件依賴
- **📦 單例管理**: 確保關鍵服務的單例模式
- **🔄 動態配置**: 支援運行時配置更新

---

### 2. 🧠 Unified Memory Manager (統一記憶體管理器)

**職責**: 提供企業級記憶體管理解決方案

#### 主要類別
- **`UnifiedMemoryManager`**: 核心記憶體管理器
- **`MemoryStrategy`**: 記憶體策略枚舉

#### 記憶體策略分類

```python
from utils import MemoryStrategy, get_memory_manager

# 可用的記憶體策略
strategies = {
    MemoryStrategy.CONSERVATIVE: "保守策略 - 最小記憶體使用",
    MemoryStrategy.BALANCED: "平衡策略 - 性能與記憶體平衡", 
    MemoryStrategy.AGGRESSIVE: "激進策略 - 最大性能優化",
    MemoryStrategy.SMART: "智能策略 - AI驅動的動態優化",
    MemoryStrategy.STREAMING: "串流策略 - 大檔案處理優化"
}
```

#### 使用範例
```python
# 基本記憶體管理
memory_manager = get_memory_manager()

# 設定記憶體策略
memory_manager.set_strategy(MemoryStrategy.SMART)

# 智能記憶體分配
with memory_manager.smart_allocation() as allocator:
    large_image = allocator.allocate_image_buffer(4096, 4096, 3)
    # 自動記憶體管理和優化

# 監控記憶體使用
memory_stats = memory_manager.get_memory_stats()
print(f"記憶體使用率: {memory_stats.usage_percent:.1f}%")
print(f"可用記憶體: {memory_stats.available_gb:.2f}GB")
```

#### 高級功能
- **🔄 動態策略切換**: 根據工作負載自動調整策略
- **📊 記憶體分析**: 詳細的記憶體使用分析和洩漏檢測
- **⚡ 快取優化**: 智能快取管理和預載機制
- **🛡️ 安全防護**: 記憶體溢出和洩漏保護

---

### 3. 📊 Unified Performance Monitor (統一性能監控器)

**職責**: 提供全方位的系統性能監控和分析

#### 主要類別
- **`UnifiedPerformanceMonitor`**: 核心性能監控器
- **`MonitoringLevel`**: 監控等級枚舉

#### 監控等級

```python
from utils import MonitoringLevel, get_performance_monitor

# 監控等級配置
monitoring_levels = {
    MonitoringLevel.BASIC: "基礎監控 - CPU、記憶體",
    MonitoringLevel.DETAILED: "詳細監控 - 包含GPU、磁碟I/O",
    MonitoringLevel.COMPREHENSIVE: "全面監控 - 所有系統指標",
    MonitoringLevel.DEBUG: "調試監控 - 包含詳細性能分析"
}
```

#### 使用範例
```python
# 啟動性能監控
monitor = get_performance_monitor()
monitor.start_monitoring(level=MonitoringLevel.COMPREHENSIVE)

# 監控特定操作
with monitor.measure_operation("image_processing") as measurement:
    # 執行圖像處理操作
    process_large_image(image_data)

# 獲取性能報告
performance_report = monitor.generate_report()
print(f"平均CPU使用率: {performance_report.avg_cpu_usage:.1f}%")
print(f"峰值記憶體使用: {performance_report.peak_memory_gb:.2f}GB")
print(f"GPU利用率: {performance_report.gpu_utilization:.1f}%")

# 設定性能警報
monitor.set_alert_threshold("cpu_usage", 85.0)
monitor.set_alert_threshold("memory_usage", 90.0)
```

#### 監控功能
- **📈 即時監控**: CPU、GPU、記憶體、磁碟、網路等即時監控
- **🎯 性能分析**: 瓶頸檢測、性能建議、優化提示
- **🚨 智能警報**: 可配置的多層級警報系統
- **📊 報告生成**: 詳細的性能分析報告

---

### 4. 🌐 Unified Distributed Processor (統一分散式處理器)

**職責**: 協調多核心和多機器的分散式處理任務

#### 主要類別
- **`UnifiedDistributedProcessor`**: 核心分散式處理器
- **`ProcessingBackend`**: 處理後端枚舉

#### 處理後端

```python
from utils import ProcessingBackend, get_distributed_processor

# 可用的處理後端
backends = {
    ProcessingBackend.LOCAL: "本地多核心處理",
    ProcessingBackend.CLUSTER: "集群分散式處理",
    ProcessingBackend.CLOUD: "雲端分散式處理",
    ProcessingBackend.HYBRID: "混合處理模式"
}
```

#### 使用範例
```python
# 初始化分散式處理器
processor = get_distributed_processor()
processor.set_backend(ProcessingBackend.LOCAL)

# 配置處理資源
processor.configure_resources(
    max_workers=8,
    memory_per_worker="2GB",
    gpu_enabled=True
)

# 分散式批次處理
image_paths = ["img1.jpg", "img2.jpg", "img3.jpg", ...]
results = processor.process_batch(
    items=image_paths,
    process_function=process_single_image,
    chunk_size=4
)

# 監控處理進度
while processor.is_running():
    progress = processor.get_progress()
    print(f"處理進度: {progress.completed}/{progress.total}")
    time.sleep(1)
```

#### 分散式功能
- **⚖️ 負載平衡**: 智能任務分配和負載平衡
- **🔄 故障恢復**: 自動故障檢測和任務重新分配
- **📊 進度追蹤**: 即時的分散式任務進度監控
- **🌍 多後端支援**: 支援本地、集群、雲端等多種後端

---

## 🚀 使用工作流程

### 基本環境設置

```python
from utils import setup_utils_environment

# 一鍵設置完整工具環境
utils_env = setup_utils_environment()

memory_manager = utils_env['memory']
performance_monitor = utils_env['performance']
distributed_processor = utils_env['distributed']

# 或個別獲取服務
from utils import get_memory_manager, get_performance_monitor, get_distributed_processor

memory_manager = get_memory_manager()
performance_monitor = get_performance_monitor()
distributed_processor = get_distributed_processor()
```

### 高性能圖像處理工作流程

```python
import time
from utils import (
    get_memory_manager, 
    get_performance_monitor, 
    get_distributed_processor,
    MemoryStrategy,
    MonitoringLevel,
    ProcessingBackend
)

def process_large_image_dataset(image_paths, output_folder):
    """高性能大型圖像數據集處理"""
    
    # 1. 設置記憶體管理
    memory_manager = get_memory_manager()
    memory_manager.set_strategy(MemoryStrategy.SMART)
    
    # 2. 啟動性能監控
    monitor = get_performance_monitor()
    monitor.start_monitoring(level=MonitoringLevel.COMPREHENSIVE)
    
    # 3. 配置分散式處理
    processor = get_distributed_processor()
    processor.set_backend(ProcessingBackend.LOCAL)
    processor.configure_resources(
        max_workers=8,
        memory_per_worker="3GB",
        gpu_enabled=True
    )
    
    # 4. 執行分散式處理
    with monitor.measure_operation("batch_processing") as measurement:
        with memory_manager.smart_allocation() as allocator:
            results = processor.process_batch(
                items=image_paths,
                process_function=lambda path: process_single_image(path, output_folder),
                chunk_size=4
            )
    
    # 5. 生成性能報告
    report = monitor.generate_report()
    print(f"處理完成！")
    print(f"總處理時間: {report.total_time:.2f}秒")
    print(f"平均CPU使用率: {report.avg_cpu_usage:.1f}%")
    print(f"記憶體峰值: {report.peak_memory_gb:.2f}GB")
    
    return results

# 使用範例
image_paths = ["image1.jpg", "image2.jpg", "image3.jpg"]
results = process_large_image_dataset(image_paths, "output/")
```

### 智能記憶體管理工作流程

```python
from utils import get_memory_manager, MemoryStrategy

def memory_intensive_processing():
    """記憶體密集型處理"""
    
    memory_manager = get_memory_manager()
    
    # 檢查系統記憶體狀況
    memory_stats = memory_manager.get_memory_stats()
    print(f"可用記憶體: {memory_stats.available_gb:.2f}GB")
    
    # 根據可用記憶體選擇策略
    if memory_stats.available_gb > 16:
        strategy = MemoryStrategy.AGGRESSIVE
    elif memory_stats.available_gb > 8:
        strategy = MemoryStrategy.BALANCED
    else:
        strategy = MemoryStrategy.CONSERVATIVE
    
    memory_manager.set_strategy(strategy)
    print(f"使用記憶體策略: {strategy.name}")
    
    # 智能記憶體分配
    with memory_manager.smart_allocation() as allocator:
        # 處理超大圖像
        large_images = []
        for i in range(10):
            image_buffer = allocator.allocate_image_buffer(8192, 8192, 3)
            large_images.append(image_buffer)
            
            # 監控記憶體使用
            current_stats = memory_manager.get_memory_stats()
            print(f"當前記憶體使用: {current_stats.usage_percent:.1f}%")
    
    # 記憶體自動釋放
    final_stats = memory_manager.get_memory_stats()
    print(f"處理完成，記憶體使用: {final_stats.usage_percent:.1f}%")

# 執行記憶體密集型處理
memory_intensive_processing()
```

### 系統監控與警報工作流程

```python
from utils import get_performance_monitor, MonitoringLevel
import time

def setup_system_monitoring():
    """設置系統監控與警報"""
    
    monitor = get_performance_monitor()
    
    # 啟動全面監控
    monitor.start_monitoring(level=MonitoringLevel.COMPREHENSIVE)
    
    # 設置多層級警報
    monitor.set_alert_threshold("cpu_usage", 80.0)      # CPU使用率超過80%
    monitor.set_alert_threshold("memory_usage", 85.0)   # 記憶體使用率超過85%
    monitor.set_alert_threshold("gpu_usage", 90.0)      # GPU使用率超過90%
    monitor.set_alert_threshold("disk_io", 1000.0)      # 磁碟I/O超過1GB/s
    
    # 設置警報回調
    def on_alert(alert_type, current_value, threshold):
        print(f"⚠️ 警報: {alert_type} = {current_value:.1f} (閾值: {threshold})")
        
        if alert_type == "memory_usage" and current_value > 90:
            print("🚨 記憶體使用率過高，建議清理快取")
        elif alert_type == "cpu_usage" and current_value > 85:
            print("🚨 CPU負載過高，建議減少並發任務")
    
    monitor.set_alert_callback(on_alert)
    
    # 即時監控循環
    try:
        while True:
            # 獲取即時狀態
            status = monitor.get_real_time_status()
            print(f"CPU: {status.cpu_usage:.1f}% | "
                  f"記憶體: {status.memory_usage:.1f}% | "
                  f"GPU: {status.gpu_usage:.1f}%")
            
            time.sleep(5)  # 每5秒更新一次
            
    except KeyboardInterrupt:
        # 生成最終報告
        final_report = monitor.generate_report()
        print("\n📊 監控報告:")
        print(f"監控時間: {final_report.monitoring_duration:.0f}秒")
        print(f"平均CPU使用率: {final_report.avg_cpu_usage:.1f}%")
        print(f"記憶體峰值: {final_report.peak_memory_gb:.2f}GB")
        print(f"警報觸發次數: {final_report.alert_count}")
        
        monitor.stop_monitoring()

# 執行系統監控
setup_system_monitoring()
```

---

## 🎯 專業工具集

### 1. 🎮 GPU Management (GPU管理)

```python
from utils.gpu_manager import get_gpu_manager

# GPU設備管理
gpu_manager = get_gpu_manager()

# 檢查GPU可用性
if gpu_manager.cuda_available:
    print(f"檢測到 {gpu_manager.device_count} 個GPU設備")
    
    # 獲取GPU狀態
    for i in range(gpu_manager.device_count):
        stats = gpu_manager.get_device_stats(i)
        print(f"GPU {i}: {stats.name}")
        print(f"  記憶體: {stats.memory_used}/{stats.memory_total}MB")
        print(f"  利用率: {stats.utilization}%")
        print(f"  溫度: {stats.temperature}°C")

# 自動選擇最佳GPU
best_gpu = gpu_manager.get_best_available_device()
print(f"建議使用GPU: {best_gpu}")
```

### 2. 🖼️ Image Processing (圖像處理)

```python
from utils.image_utils import ImageProcessor

# 高性能圖像處理
processor = ImageProcessor()

# 智能圖像載入
image = processor.smart_load("large_image.jpg", 
                           max_size=(4096, 4096),
                           auto_optimize=True)

# 批次圖像處理
image_paths = ["img1.jpg", "img2.jpg", "img3.jpg"]
processed_images = processor.batch_process(
    image_paths,
    operations=["resize", "normalize", "enhance"],
    output_format="jpg",
    quality=95
)

# 記憶體友善的大圖像處理
with processor.memory_efficient_mode():
    huge_image = processor.load_large_image("huge_panorama.jpg")
    processed = processor.apply_transformations(huge_image)
```

### 3. 📁 File Operations (檔案操作)

```python
from utils.file_utils import FileManager

# 智能檔案管理
file_manager = FileManager()

# 安全檔案操作
with file_manager.safe_operations() as ops:
    # 智能複製（自動處理衝突）
    ops.smart_copy("source/", "destination/", 
                   conflict_resolution="rename")
    
    # 批次檔案處理
    image_files = ops.find_files("input/", 
                                patterns=["*.jpg", "*.png"],
                                recursive=True)
    
    # 並行檔案操作
    ops.parallel_process(image_files, 
                        operation="compress",
                        max_workers=4)

# 檔案系統監控
file_manager.monitor_directory("watch_folder/", 
                              on_change=lambda path: print(f"檔案變更: {path}"))
```

### 4. 🏷️ LabelMe Processing (標註處理)

```python
from utils.labelme_processor import LabelMeProcessor

# LabelMe標註處理
processor = LabelMeProcessor()

# 批次處理LabelMe標註檔案
annotation_files = ["anno1.json", "anno2.json", "anno3.json"]
processed_annotations = processor.batch_process(annotation_files)

# 標註格式轉換
converted = processor.convert_format(
    input_format="labelme",
    output_format="yolo",
    annotations=processed_annotations
)

# 標註品質檢查
quality_report = processor.quality_check(processed_annotations)
print(f"標註品質分數: {quality_report.quality_score:.2f}")
```

### 5. ⚖️ Load Balancing (負載平衡)

```python
from utils.smart_load_balancer import SmartLoadBalancer

# 智能負載平衡器
balancer = SmartLoadBalancer()

# 配置工作節點
balancer.add_worker("worker1", capacity=4, gpu_enabled=True)
balancer.add_worker("worker2", capacity=2, gpu_enabled=False)
balancer.add_worker("worker3", capacity=8, gpu_enabled=True)

# 智能任務分配
tasks = ["task1", "task2", "task3", "task4", "task5"]
assignments = balancer.distribute_tasks(
    tasks,
    strategy="capacity_aware",  # 根據容量分配
    consider_gpu=True          # 考慮GPU需求
)

print("任務分配結果:")
for worker, assigned_tasks in assignments.items():
    print(f"{worker}: {assigned_tasks}")

# 動態負載監控
balancer.start_monitoring()
while balancer.is_balanced():
    status = balancer.get_balance_status()
    print(f"系統平衡度: {status.balance_score:.2f}")
    time.sleep(5)
```

---

## 🔧 配置與自定義

### 1. 工具配置

```python
from utils.core.config import UtilsConfig

# 創建自定義配置
config = UtilsConfig(
    # 記憶體配置
    memory_strategy="smart",
    max_memory_usage_percent=80.0,
    enable_memory_monitoring=True,
    
    # 性能監控配置
    monitoring_level="comprehensive",
    performance_alert_thresholds={
        "cpu_usage": 85.0,
        "memory_usage": 90.0,
        "gpu_usage": 95.0
    },
    
    # 分散式處理配置
    processing_backend="local",
    max_workers=8,
    worker_memory_limit="4GB",
    
    # GPU配置
    gpu_enabled=True,
    prefer_gpu_processing=True,
    gpu_memory_fraction=0.8
)

# 應用配置
from utils import container
container.core.config.update(config)
```

### 2. 自定義服務提供者

```python
from utils.providers.memory_providers import MemoryProvider

class CustomMemoryProvider(MemoryProvider):
    """自定義記憶體提供者"""
    
    def allocate(self, size: int, strategy: str = "default"):
        # 自定義記憶體分配邏輯
        return super().allocate(size, strategy)
    
    def optimize(self, current_usage: float):
        # 自定義優化邏輯
        if current_usage > 0.9:
            self.trigger_cleanup()
        return super().optimize(current_usage)

# 註冊自定義提供者
from utils import container
container.register_memory_provider(CustomMemoryProvider())
```

### 3. 擴展監控指標

```python
from utils.core.metric_collector import MetricCollector

class CustomMetricCollector(MetricCollector):
    """自定義指標收集器"""
    
    def collect_custom_metrics(self):
        """收集自定義指標"""
        metrics = {}
        
        # 添加應用特定指標
        metrics["panorama_processing_rate"] = self.get_processing_rate()
        metrics["detection_accuracy"] = self.get_detection_accuracy()
        metrics["cube_generation_time"] = self.get_cube_generation_time()
        
        return metrics

# 註冊自定義指標收集器
monitor = get_performance_monitor()
monitor.add_metric_collector(CustomMetricCollector())
```

---

## 🤝 與其他模組關係

### 依賴關係圖

```mermaid
graph TD
    A[Utils Module] --> B[Config Module]
    A --> C[Log Utils Module]
    D[Core Module] --> A
    E[Detection Module] --> A
    F[Processing Module] --> A
    
    subgraph "Utils Internal"
        G[Unified Memory Manager] --> H[Memory Providers]
        I[Unified Performance Monitor] --> J[Metric Collectors]
        K[Unified Distributed Processor] --> L[Processing Providers]
        M[DI Container] --> G
        M --> I
        M --> K
    end
```

### 1. 🔗 與Core模組關係 (工具支援)

```python
# utils為core模組提供基礎支援
relationships = {
    "性能監控": "utils.performance_monitor → core.coordinate統計追蹤",
    "記憶體管理": "utils.memory_utils → core.interpolation大數據處理",
    "GPU管理": "utils.gpu_manager → core.projection硬體加速"
}
```

**關係說明**:
- **基礎設施**: utils為core提供記憶體、性能、GPU等基礎設施
- **優化支援**: 通過智能管理提升core模組的處理效能
- **資源協調**: 協調系統資源以支援core的計算密集型任務

### 2. 📊 與Processing模組關係 (服務支援)

```python
# utils為processing提供企業級服務支援
services = {
    "processing/components/memory_pool.py": [
        "from utils import get_memory_manager",
        "# 使用統一記憶體管理器"
    ],
    "processing/components/parallel_coordinator.py": [
        "from utils import get_distributed_processor", 
        "# 使用分散式處理協調器"
    ]
}
```

**關係說明**:
- **服務提供者**: utils為processing提供記憶體、監控、分散式等服務
- **性能優化**: 通過智能資源管理提升processing的整體性能
- **企業級功能**: 提供監控、警報、負載平衡等企業級功能

### 3. 🔍 與Detection模組關係 (優化支援)

```python
# utils為detection提供性能優化支援
optimizations = [
    "GPU記憶體管理 → AI模型載入優化",
    "性能監控 → 檢測速度分析",
    "分散式處理 → 批次檢測加速"
]
```

### 4. 📝 與Log Utils模組關係 (協作)

```python
# utils與log_utils協作提供完整監控
collaboration = {
    "事件記錄": "utils性能事件 → log_utils結構化記錄",
    "警報集成": "utils警報系統 → log_utils警報記錄",
    "統計報告": "utils性能統計 → log_utils報告生成"
}
```

### 5. ⚙️ 與Config模組關係 (配置驅動)

```python
# utils的行為完全由config驅動
from config.settings import get_config

config = get_config()

# utils根據config調整行為
memory_manager.configure(
    strategy=config.system.memory_strategy,
    max_usage=config.system.max_memory_percent
)

performance_monitor.configure(
    level=config.system.monitoring_level,
    thresholds=config.system.alert_thresholds
)
```

---

## 🧪 測試與驗證

### 測試覆蓋範圍

```python
test_coverage = {
    "utils/tests/": {
        "test_memory_manager.py": "記憶體管理器測試",
        "test_performance_monitor.py": "性能監控器測試",
        "test_distributed_processor.py": "分散式處理器測試",
        "test_gpu_manager.py": "GPU管理器測試",
        "test_file_utils.py": "檔案工具測試",
        "test_image_utils.py": "圖像工具測試",
        "test_integration.py": "整合測試"
    }
}
```

### 性能基準測試

```python
# 執行utils性能測試
def benchmark_utils_performance():
    """測試utils模組性能"""
    
    # 記憶體管理性能測試
    memory_manager = get_memory_manager()
    start_time = time.time()
    
    with memory_manager.smart_allocation() as allocator:
        # 分配大量記憶體
        buffers = []
        for i in range(100):
            buffer = allocator.allocate_buffer(1024 * 1024)  # 1MB each
            buffers.append(buffer)
    
    memory_time = time.time() - start_time
    print(f"記憶體管理效能: {memory_time:.3f}秒")
    
    # 性能監控開銷測試
    monitor = get_performance_monitor()
    monitor.start_monitoring()
    
    start_time = time.time()
    # 模擬工作負載
    for i in range(1000):
        monitor.record_metric("test_metric", i * 0.1)
    
    monitoring_overhead = time.time() - start_time
    print(f"監控開銷: {monitoring_overhead:.3f}秒")
    
    monitor.stop_monitoring()

# 執行基準測試
benchmark_utils_performance()
```

### 壓力測試

```python
def stress_test_utils():
    """utils模組壓力測試"""
    
    # 記憶體壓力測試
    memory_manager = get_memory_manager()
    
    try:
        large_allocations = []
        while True:
            # 持續分配記憶體直到接近限制
            allocation = memory_manager.allocate_buffer(100 * 1024 * 1024)  # 100MB
            large_allocations.append(allocation)
            
            stats = memory_manager.get_memory_stats()
            if stats.usage_percent > 85:
                print("達到記憶體使用限制，測試通過")
                break
                
    except MemoryError:
        print("記憶體保護機制正常工作")
    
    # 清理測試資源
    del large_allocations
    memory_manager.force_cleanup()

# 執行壓力測試
stress_test_utils()
```

---

## ⚠️ 故障排除

### 1. 記憶體問題

```python
# 記憶體洩漏檢測
from utils import get_memory_manager

def detect_memory_leaks():
    """檢測記憶體洩漏"""
    memory_manager = get_memory_manager()
    
    # 啟用記憶體追蹤
    memory_manager.enable_leak_detection()
    
    # 執行可能洩漏的操作
    for i in range(100):
        buffer = memory_manager.allocate_buffer(1024)
        # 故意不釋放部分記憶體
        if i % 10 != 0:
            memory_manager.deallocate(buffer)
    
    # 檢查洩漏報告
    leak_report = memory_manager.get_leak_report()
    if leak_report.leaked_objects > 0:
        print(f"檢測到 {leak_report.leaked_objects} 個洩漏對象")
        print(f"洩漏記憶體: {leak_report.leaked_memory_mb:.2f}MB")
    
    # 強制清理
    memory_manager.force_cleanup()

# 執行洩漏檢測
detect_memory_leaks()
```

### 2. 性能問題

```python
# 性能瓶頸診斷
from utils import get_performance_monitor

def diagnose_performance_issues():
    """診斷性能瓶頸"""
    monitor = get_performance_monitor()
    
    # 啟動詳細監控
    monitor.start_monitoring(level=MonitoringLevel.DEBUG)
    
    # 模擬可能的性能問題
    import time
    
    # CPU密集型任務
    start = time.time()
    for i in range(1000000):
        _ = i ** 2
    cpu_time = time.time() - start
    
    # 記憶體密集型任務
    start = time.time()
    large_list = [i for i in range(1000000)]
    memory_time = time.time() - start
    
    # 生成診斷報告
    diagnosis = monitor.diagnose_performance()
    print("性能診斷報告:")
    print(f"CPU瓶頸: {diagnosis.cpu_bottleneck}")
    print(f"記憶體瓶頸: {diagnosis.memory_bottleneck}")
    print(f"I/O瓶頸: {diagnosis.io_bottleneck}")
    print(f"建議優化: {diagnosis.optimization_suggestions}")
    
    monitor.stop_monitoring()

# 執行性能診斷
diagnose_performance_issues()
```

### 3. 分散式處理問題

```python
# 分散式處理故障診斷
from utils import get_distributed_processor

def diagnose_distributed_issues():
    """診斷分散式處理問題"""
    processor = get_distributed_processor()
    
    # 檢查工作節點狀態
    nodes_status = processor.check_nodes_health()
    
    for node_id, status in nodes_status.items():
        print(f"節點 {node_id}:")
        print(f"  狀態: {status.health}")
        print(f"  負載: {status.load:.1f}")
        print(f"  記憶體使用: {status.memory_usage:.1f}%")
        
        if not status.healthy:
            print(f"  ⚠️ 節點異常: {status.error_message}")

# 執行分散式診斷
diagnose_distributed_issues()
```

---

## 📈 版本資訊與更新日誌

### 當前版本: 1.0.0

#### ✨ 主要特性
- 🏗️ 統一架構設計（三大管理器 + DI容器）
- 🧠 智能記憶體管理（5種策略，自適應優化）
- 📊 全方位性能監控（4個監控等級，智能警報）
- 🌐 分散式處理支援（多後端，負載平衡）
- 🎮 專業GPU管理（多設備支援，智能調度）
- 🖼️ 高性能圖像處理工具
- 📁 智能檔案系統操作
- 🔄 完整向後兼容性

#### 🔄 重構亮點
- 從分散的多個獨立工具整合為統一生態系統
- 採用依賴注入容器管理組件生命週期
- 現代化API設計，支援異步和上下文管理
- 企業級錯誤處理和恢復機制

### 📋 未來發展計劃

#### 版本 1.1.0 (計劃)
- [ ] 異步處理支援（async/await模式）
- [ ] 雲端存儲集成（AWS S3, Azure Blob等）
- [ ] 容器化部署支援（Docker, Kubernetes）
- [ ] Web API服務介面

#### 版本 1.2.0 (計劃)
- [ ] 機器學習輔助優化（AI驅動的資源管理）
- [ ] 區塊鏈分散式計算支援
- [ ] 即時協作功能（多用戶協同處理）
- [ ] 視覺化管理介面

---

**🛠️ Utils Module - 360° 全景影像處理系統的企業級工具生態系統**

> 通過統一設計和智能管理，為高性能影像處理提供穩定、高效、可擴展的基礎設施支援。