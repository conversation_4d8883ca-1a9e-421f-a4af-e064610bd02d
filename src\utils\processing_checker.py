"""
處理檢查工具模組
從舊版本main.py中提取的處理檢查相關功能

主要功能：
- 檢查圖像是否需要處理
- 驗證處理結果完整性  
- 計算處理統計信息
- 檢查資料夾結構
- 進度檔案管理
"""

import os
import time
import logging
import csv
from datetime import datetime
from typing import Any
from pathlib import Path
import sys

# Import project modules directly (using pip install -e .)
from utils import import_helper

from config.constants import SUPPORTED_IMAGE_EXTS
from utils.file_utils import FileUtils
from processing.panorama_processor import PanoramaProcessor, SaveMode

logger = logging.getLogger('processing_checker')


class ProcessingChecker:
    """
    處理檢查器 - 從舊版本main.py的檢查功能重構
    
    支援功能：
    - 檢查圖像是否需要模糊處理
    - 驗證輸出檔案完整性
    - 計算處理統計信息
    - 管理進度檔案
    """
    
    def __init__(self, detector=None):
        """
        初始化處理檢查器
        
        Args:
            detector: 檢測器實例
        """
        self.detector = detector
        self.file_utils = FileUtils()
        
    def check_image_needs_processing(self, img_path: str,
                                   save_mode: SaveMode = SaveMode.ALL,
                                   has_logo: bool = False) -> tuple[bool, dict]:
        """
        檢查圖像是否需要處理（從舊版本main.py移植）
        
        Args:
            img_path: 圖像路徑
            save_mode: 儲存模式
            has_logo: 是否有標誌
            
        Returns:
            (是否需要處理, 檢測統計)
        """
        try:
            # 如果是只保存標誌模式且沒有標誌，直接返回不需要處理
            if save_mode == SaveMode.LOGO_ONLY and not has_logo:
                return False, {}
            
            # 如果有標誌且模式包含標誌，則需要處理
            if has_logo and (save_mode == SaveMode.ALL or 
                           save_mode == SaveMode.LOGO_ONLY or 
                           save_mode == SaveMode.LOGO_AND_BLUR):
                return True, {}
            
            # 如果沒有檢測器，只能處理標誌
            if self.detector is None:
                return has_logo, {}
                
            # 進行檢測檢查
            detection_stats = self._check_image_detections(img_path)
            needs_processing = any(count > 0 for count in detection_stats.values())
            
            # 根據儲存模式決定是否需要處理
            if save_mode == SaveMode.BLUR_ONLY or save_mode == SaveMode.LOGO_AND_BLUR:
                return needs_processing, detection_stats
            elif save_mode == SaveMode.ALL:
                return needs_processing or has_logo, detection_stats
            
            return False, detection_stats
            
        except Exception as e:
            logger.error(f"檢查圖像 {img_path} 是否需要處理時發生錯誤: {e}")
            return False, {}
    
    def _check_image_detections(self, img_path: str) -> dict[int, int]:
        """
        檢查圖像中的檢測結果
        
        Args:
            img_path: 圖像路徑
            
        Returns:
            檢測統計字典 {face_id: detection_count}
        """
        try:
            # 載入圖像並轉換為立方體映射
            processor = PanoramaProcessor(detector=self.detector)
            needs_processing, stats = processor.check_panorama_needs_processing(
                img_path, SaveMode.ALL)
            
            return stats
            
        except Exception as e:
            logger.error(f"檢測圖像 {img_path} 時發生錯誤: {e}")
            return {}
    
    def verify_processing_output(self, output_folder: str,
                               expected_files: list[str] | None = None) -> tuple[bool, dict]:
        """
        驗證處理輸出的完整性
        
        Args:
            output_folder: 輸出資料夾
            expected_files: 預期的檔案列表
            
        Returns:
            (是否完整, 檢查結果)
        """
        if expected_files is None:
            expected_files = [
                'html5/0.jpg', 'html5/1.jpg', 'html5/2.jpg',
                'html5/3.jpg', 'html5/4.jpg', 'html5/5.jpg',
                'preview.jpg', 'thumbnail.jpg'
            ]
        
        result = {
            'missing_files': [],
            'invalid_files': [],
            'total_files': len(expected_files),
            'valid_files': 0
        }
        
        try:
            for file_path in expected_files:
                full_path = os.path.join(output_folder, file_path)
                
                if not os.path.exists(full_path):
                    result['missing_files'].append(file_path)
                    continue
                
                # 檢查檔案是否有效
                if self._is_valid_image_file(full_path):
                    result['valid_files'] += 1
                else:
                    result['invalid_files'].append(file_path)
            
            is_complete = (len(result['missing_files']) == 0 and 
                          len(result['invalid_files']) == 0)
            
            return is_complete, result
            
        except Exception as e:
            logger.error(f"驗證輸出 {output_folder} 時發生錯誤: {e}")
            return False, result
    
    def _is_valid_image_file(self, file_path: str) -> bool:
        """檢查圖像檔案是否有效"""
        try:
            import cv2
            img = cv2.imread(file_path)
            return img is not None and img.size > 0
        except Exception:
            return False
    
    def calculate_folder_statistics(self, base_folder: str) -> dict[str, Any]:
        """
        計算資料夾的處理統計信息
        
        Args:
            base_folder: 基礎資料夾
            
        Returns:
            統計信息字典
        """
        stats = {
            'total_images': 0,
            'processed_images': 0,
            'needs_processing': 0,
            'has_blur': 0,
            'has_logo': 0,
            'total_blur_regions': 0,
            'processing_rate': 0.0,
            'blur_rate': 0.0
        }
        
        try:
            # 遍歷所有圖像檔案
            for root, dirs, files in os.walk(base_folder):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in SUPPORTED_IMAGE_EXTS):
                        stats['total_images'] += 1
                        
                        img_path = os.path.join(root, file)
                        needs_processing, detection_stats = self.check_image_needs_processing(img_path)
                        
                        if needs_processing:
                            stats['needs_processing'] += 1
                        
                        # 檢查是否有模糊區域
                        total_detections = sum(detection_stats.values())
                        if total_detections > 0:
                            stats['has_blur'] += 1
                            stats['total_blur_regions'] += total_detections
                        
                        # 檢查對應的輸出是否存在
                        output_folder = self._get_output_folder_for_image(img_path, base_folder)
                        if output_folder and os.path.exists(output_folder):
                            is_complete, _ = self.verify_processing_output(output_folder)
                            if is_complete:
                                stats['processed_images'] += 1
            
            # 計算比率
            if stats['total_images'] > 0:
                stats['processing_rate'] = stats['processed_images'] / stats['total_images']
                stats['blur_rate'] = stats['has_blur'] / stats['total_images']
            
            return stats
            
        except Exception as e:
            logger.error(f"計算資料夾統計時發生錯誤: {e}")
            return stats
    
    def _get_output_folder_for_image(self, img_path: str, base_folder: str) -> str | None:
        """獲取圖像對應的輸出資料夾路徑"""
        try:
            # 這裡需要根據實際的輸出結構來判斷
            # 暫時返回None，實際使用時需要根據具體邏輯實現
            return None
        except Exception:
            return None
    
    def check_folder_structure(self, folder_path: str) -> dict[str, Any]:
        """
        檢查資料夾結構是否符合預期
        
        Args:
            folder_path: 資料夾路徑
            
        Returns:
            結構檢查結果
        """
        result = {
            'is_valid': False,
            'structure_type': 'unknown',
            'issues': [],
            'recommendations': []
        }
        
        try:
            if not os.path.exists(folder_path):
                result['issues'].append('資料夾不存在')
                return result
            
            # 檢查是否為區域結構
            if self._is_district_structure(folder_path):
                result['structure_type'] = 'district'
                result['is_valid'] = True
            # 檢查是否為場景結構
            elif self._is_scene_structure(folder_path):
                result['structure_type'] = 'scene'
                result['is_valid'] = True
            # 檢查是否為簡單圖像資料夾
            elif self._is_image_folder(folder_path):
                result['structure_type'] = 'images'
                result['is_valid'] = True
            else:
                result['issues'].append('無法識別的資料夾結構')
                result['recommendations'].append('確保資料夾包含圖像檔案或符合區域/場景結構')
            
            return result
            
        except Exception as e:
            logger.error(f"檢查資料夾結構時發生錯誤: {e}")
            result['issues'].append(f'檢查失敗: {str(e)}')
            return result
    
    def _is_district_structure(self, folder_path: str) -> bool:
        """檢查是否為區域結構（包含「區」字的子目錄）"""
        try:
            for item in os.listdir(folder_path):
                item_path = os.path.join(folder_path, item)
                if os.path.isdir(item_path) and '區' in item:
                    return True
            return False
        except Exception:
            return False
    
    def _is_scene_structure(self, folder_path: str) -> bool:
        """檢查是否為場景結構（包含場景子目錄）"""
        try:
            has_subdirs = False
            has_images_in_subdirs = False
            
            for item in os.listdir(folder_path):
                item_path = os.path.join(folder_path, item)
                if os.path.isdir(item_path):
                    has_subdirs = True
                    # 檢查子目錄中是否有圖像
                    for subitem in os.listdir(item_path):
                        if any(subitem.lower().endswith(ext) for ext in SUPPORTED_IMAGE_EXTS):
                            has_images_in_subdirs = True
                            break
                    if has_images_in_subdirs:
                        break
            
            return has_subdirs and has_images_in_subdirs
        except Exception:
            return False
    
    def _is_image_folder(self, folder_path: str) -> bool:
        """檢查是否為圖像資料夾（直接包含圖像檔案）"""
        try:
            for item in os.listdir(folder_path):
                if any(item.lower().endswith(ext) for ext in SUPPORTED_IMAGE_EXTS):
                    return True
            return False
        except Exception:
            return False


class ProgressManager:
    """
    🚨 已棄用 - 請使用 utils.file_utils.ProgressManager
    
    進度管理器 - 從舊版本main.py的進度檔案功能重構
    此類已被更完整的 file_utils.ProgressManager 取代
    """
    
    def __init__(self, progress_file: str):
        """
        初始化進度管理器
        
        Args:
            progress_file: 進度檔案路徑
        """
        self.progress_file = progress_file
        self.ensure_progress_file()
    
    def ensure_progress_file(self):
        """確保進度檔案存在"""
        if not os.path.exists(self.progress_file):
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                f.write("時間,區域,場景,狀態\n")
    
    def save_progress(self, district: str, scene: str, status: str):
        """
        保存進度記錄
        
        Args:
            district: 區域名稱
            scene: 場景名稱  
            status: 處理狀態
        """
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            with open(self.progress_file, 'a', encoding='utf-8') as f:
                f.write(f"{timestamp},{district},{scene},{status}\n")
        except Exception as e:
            logger.error(f"保存進度時發生錯誤: {e}")
    
    def load_progress(self) -> tuple[str | None, str | None, str | None]:
        """
        讀取最後的進度記錄
        
        Returns:
            (區域, 場景, 狀態)
        """
        try:
            if not os.path.exists(self.progress_file):
                return None, None, None
            
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if len(lines) <= 1:  # 只有標題行
                    return None, None, None
                
                # 找出最後一個非空行
                last_line = None
                for line in reversed(lines):
                    if line.strip():
                        last_line = line.strip()
                        break
                
                if last_line:
                    parts = last_line.split(',')
                    if len(parts) >= 4:
                        return parts[1], parts[2], parts[3]
                        
        except Exception as e:
            logger.error(f"讀取進度時發生錯誤: {e}")
        
        return None, None, None
    
    def get_progress_summary(self) -> dict[str, Any]:
        """
        獲取進度摘要
        
        Returns:
            進度摘要字典
        """
        summary = {
            'total_records': 0,
            'completed_districts': set(),
            'completed_scenes': set(),
            'failed_items': [],
            'last_update': None
        }
        
        try:
            if not os.path.exists(self.progress_file):
                return summary
            
            with open(self.progress_file, 'r', encoding='utf-8') as f:
                reader = csv.reader(f)
                next(reader)  # 跳過標題行
                
                for row in reader:
                    if len(row) >= 4:
                        summary['total_records'] += 1
                        timestamp, district, scene, status = row[:4]
                        
                        if status == "完成":
                            summary['completed_districts'].add(district)
                            summary['completed_scenes'].add(f"{district}/{scene}")
                        elif status == "失敗":
                            summary['failed_items'].append({
                                'district': district,
                                'scene': scene,
                                'timestamp': timestamp
                            })
                        
                        summary['last_update'] = timestamp
            
            # 轉換集合為計數
            summary['completed_districts'] = len(summary['completed_districts'])
            summary['completed_scenes'] = len(summary['completed_scenes'])
            
        except Exception as e:
            logger.error(f"獲取進度摘要時發生錯誤: {e}")
        
        return summary


def main():
    """模組使用示例"""
    print("=== 處理檢查工具模組 ===")
    print("\n功能說明：")
    print("1. 檢查圖像是否需要處理")
    print("2. 驗證處理結果完整性")
    print("3. 計算處理統計信息")
    print("4. 檢查資料夾結構")
    print("5. 進度檔案管理")
    
    print("\n使用示例：")
    print("```python")
    print("from utils.processing_checker import ProcessingChecker")
    print("from utils.file_utils import ProgressManager  # 統一的進度管理器")
    print("from processing.panorama_processor import SaveMode")
    print()
    print("# 創建檢查器")
    print("checker = ProcessingChecker(detector=None)")
    print()
    print("# 檢查圖像是否需要處理")
    print("needs_processing, stats = checker.check_image_needs_processing(")
    print("    'image.jpg', SaveMode.ALL, has_logo=True)")
    print("print(f'需要處理: {needs_processing}, 統計: {stats}')")
    print()
    print("# 驗證處理輸出")
    print("is_complete, result = checker.verify_processing_output('output_folder')")
    print("print(f'處理完整: {is_complete}')")
    print("print(f'缺失檔案: {result[\"missing_files\"]}')")
    print()
    print("# 計算資料夾統計")
    print("stats = checker.calculate_folder_statistics('input_folder')")
    print("print(f'總圖像: {stats[\"total_images\"]}')")
    print("print(f'處理率: {stats[\"processing_rate\"]:.2%}')")
    print()
    print("# 檢查資料夾結構")
    print("structure = checker.check_folder_structure('folder_path')")
    print("print(f'結構類型: {structure[\"structure_type\"]}')")
    print("print(f'是否有效: {structure[\"is_valid\"]}')")
    print()
    print("# 進度管理")
    print("progress_mgr = ProgressManager('progress.csv')")
    print("progress_mgr.save_progress('區域1', '場景1', '完成')")
    print("district, scene, status = progress_mgr.load_progress()")
    print("summary = progress_mgr.get_progress_summary()")
    print("print(f'已完成區域: {summary[\"completed_districts\"]}')")
    print("```")
    
    print("\n進階功能：")
    print("- 智能檢測需求分析")
    print("- 輸出完整性驗證")
    print("- 詳細統計報告")
    print("- 靈活的進度管理")
    print("- 資料夾結構智能識別")


if __name__ == "__main__":
    main()