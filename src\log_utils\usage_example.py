#!/usr/bin/env python3
"""
Log Utils v2.0 使用示例

展示重構後日誌模組的各種使用方式
"""

import logging
import sys
from pathlib import Path

# Import project modules directly (using pip install -e .)


def example_basic_usage():
    """基本使用示例"""
    print("=== 基本使用示例 ===")

    from log_utils import setup_logger

    # 創建基本日誌器
    logger = setup_logger(
        name="example_app",
        level=logging.INFO,
        console_output=True,
        file_output=False,  # 避免創建文件
        use_colors=True,
    )

    logger.info("應用啟動")
    logger.warning("這是一個警告")
    logger.error("這是一個錯誤")
    print()


def example_config_usage():
    """配置系統使用示例"""
    print("=== 配置系統使用示例 ===")

    from log_utils.core.config import LogConfig, LogConfigPresets
    from log_utils.core.manager import LogManager

    # 使用預設配置
    config = LogConfigPresets.development()
    config.name = "config_example"
    config.file_output = False  # 避免創建文件

    # 創建管理器
    manager = LogManager(config)
    logger = manager.setup_logger("config_example")

    logger.debug("這是調試信息")
    logger.info("這是普通信息")
    logger.warning("這是警告信息")
    print()


def example_formatters():
    """格式器使用示例"""
    print("=== 格式器使用示例 ===")

    from log_utils.formatters import (ColoredFormatter, CompactFormatter,
                                      JSONFormatter)
    from log_utils.handlers import create_console_handler

    # 創建不同格式的處理器
    formatters = [
        ("彩色格式器", ColoredFormatter("%(levelname)s - %(message)s")),
        ("JSON格式器", JSONFormatter()),
        ("緊湊格式器", CompactFormatter()),
    ]

    for name, formatter in formatters:
        print(f"{name}:")
        handler = create_console_handler(level=logging.INFO, use_colors=False)
        handler.setFormatter(formatter)

        logger = logging.getLogger(f"formatter_test_{name}")
        logger.handlers.clear()
        logger.addHandler(handler)
        logger.setLevel(logging.INFO)
        logger.propagate = False

        logger.info("測試消息")
        print()


def example_presets():
    """預設配置示例"""
    print("=== 預設配置示例 ===")

    from log_utils.factory import create_logger_from_preset

    presets = ["development", "production", "debug", "minimal"]

    for preset in presets:
        logger = create_logger_from_preset(
            f"preset_{preset}", preset, file_output=False
        )
        logger.info(f"使用 {preset} 預設配置")

    print()


def example_advanced_features():
    """高級功能示例"""
    print("=== 高級功能示例 ===")

    from log_utils.factory import (create_debug_logger, get_logging_statistics,
                                   list_loggers, set_logger_level)

    # 創建調試日誌器
    debug_logger = create_debug_logger("debug_example", file_output=False)
    debug_logger.debug("這是調試信息")

    # 顯示統計信息
    stats = get_logging_statistics()
    print(f"目前有 {stats['total_loggers']} 個日誌器")

    # 列出所有日誌器
    loggers = list_loggers()
    print(f"日誌器列表: {loggers}")

    # 設置日誌器級別
    success = set_logger_level("debug_example", logging.WARNING)
    print(f"設置級別: {'成功' if success else '失敗'}")

    print()


def example_backward_compatibility():
    """向後兼容性示例"""
    print("=== 向後兼容性示例 ===")

    from log_utils import (create_tool_logger, setup_basic_logger,
                           setup_simple_logger)

    # 舊的API仍然可用
    basic_logger = setup_basic_logger("old_basic", file_output=False)
    basic_logger.info("舊的基本日誌器")

    tool_logger = create_tool_logger("old_tool", file_output=False)
    tool_logger.info("舊的工具日誌器")

    simple_logger = setup_simple_logger("old_simple", file_output=False)
    simple_logger.info("舊的簡單日誌器")

    print()


def example_error_handling():
    """錯誤處理示例"""
    print("=== 錯誤處理示例 ===")

    from log_utils.core.config import LogConfig
    from log_utils.core.manager import LogManager

    # 測試配置驗證
    try:
        invalid_config = LogConfig(
            name="",  # 無效名稱
            level=999,  # 無效級別
            console_output=False,
            file_output=False,  # 沒有輸出方式
        )
        manager = LogManager(invalid_config)
    except ValueError as e:
        print(f"配置驗證成功捕獲錯誤: {e}")

    print()


def main():
    """主函數"""
    print("🚀 Log Utils v2.0 使用示例")
    print("=" * 50)

    examples = [
        example_basic_usage,
        example_config_usage,
        example_formatters,
        example_presets,
        example_advanced_features,
        example_backward_compatibility,
        example_error_handling,
    ]

    for example in examples:
        try:
            example()
        except Exception as e:
            print(f"❌ 示例執行失敗: {e}")

    print("=" * 50)
    print("✅ 所有示例執行完成")


if __name__ == "__main__":
    main()
