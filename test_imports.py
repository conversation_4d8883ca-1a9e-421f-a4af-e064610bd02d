#!/usr/bin/env python3
"""
導入測試腳本
用於驗證所有模組的 __init__.py 文件是否正確設置，並且所有導入路徑都能正常工作。
"""

import sys
import traceback
from pathlib import Path

def test_import(module_name: str) -> bool:
    """測試單個模組的導入"""
    try:
        __import__(module_name)
        print(f"[OK] {module_name}")
        return True
    except ImportError as e:
        print(f"[FAIL] {module_name}: {e}")
        return False
    except Exception as e:
        print(f"[ERROR] {module_name}: Unexpected error - {e}")
        return False

def test_specific_imports():
    """測試特定的重要導入"""
    import_tests = [
        # 主要模組
        "config",
        "core",
        "detection", 
        "log_utils",
        "processing",
        "utils",
        
        # config 子模組
        "config.constants",
        "config.settings",
        "config.interpolation",
        
        # core 子模組  
        "core.coordinate",
        "core.cube_mapping",
        "core.interpolation",
        "core.projection",
        "core.samplers",
        
        # detection 子模組
        "detection.core",
        "detection.models", 
        "detection.strategies",
        "detection.postprocessing",
        "detection.utils",
        
        # processing 子模組
        "processing.components",
        
        # utils 子模組
        "utils.core",
        "utils.providers",
        
        # log_utils 子模組
        "log_utils.core",
    ]
    
    print("=== 測試模組導入 ===")
    success_count = 0
    total_count = len(import_tests)
    
    for module in import_tests:
        if test_import(module):
            success_count += 1
    
    print(f"\n總結: {success_count}/{total_count} 個模組成功導入")
    return success_count == total_count

def test_main_functionality():
    """測試主要功能是否可用"""
    print("\n=== 測試主要功能 ===")
    
    try:
        # 測試 config
        from config import get_config, Face, SaveMode
        config = get_config()
        print("[OK] config main functionality available")
    except Exception as e:
        print(f"[FAIL] config functionality test failed: {e}")
        
    try:
        # 測試 core
        from core import CoordinateTransformer, CubeMapper
        print("[OK] core main functionality available")
    except Exception as e:
        print(f"[FAIL] core functionality test failed: {e}")
        
    try:
        # 測試 detection
        from detection import Detector, DetectionConfig
        print("[OK] detection main functionality available")
    except Exception as e:
        print(f"[FAIL] detection functionality test failed: {e}")
        
    try:
        # 測試 processing
        from processing import PanoramaProcessor, AdvancedBatchProcessor
        print("[OK] processing main functionality available")
    except Exception as e:
        print(f"[FAIL] processing functionality test failed: {e}")
        
    try:
        # 測試 utils
        from utils import get_memory_manager, get_performance_monitor
        print("[OK] utils main functionality available")
    except Exception as e:
        print(f"[FAIL] utils functionality test failed: {e}")
        
    try:
        # 測試 log_utils
        from log_utils import setup_logger, get_logger
        print("[OK] log_utils main functionality available")
    except Exception as e:
        print(f"[FAIL] log_utils functionality test failed: {e}")

def main():
    """主測試函數"""
    print("全景影像處理系統 - 導入測試")
    print("=" * 50)
    
    # 測試基本導入
    all_imports_ok = test_specific_imports()
    
    # 測試主要功能
    test_main_functionality()
    
    print("\n" + "=" * 50)
    if all_imports_ok:
        print("SUCCESS: All module import tests passed!")
        return 0
    else:
        print("WARNING: Some module imports failed, please check related files.")
        return 1

if __name__ == "__main__":
    sys.exit(main())