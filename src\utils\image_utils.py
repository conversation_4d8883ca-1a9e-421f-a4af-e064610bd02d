"""
圖像處理工具模組

提供圖像處理相關的工具函數，支援：
- 圖像讀寫和格式轉換
- 圖像驗證和信息獲取
- 預覽圖像生成
- 圖像尺寸調整和處理
- 批次圖像操作
"""

import cv2
import numpy as np
import os
import imghdr
import logging
from typing import Any
from PIL import Image, UnidentifiedImageError
from dataclasses import dataclass
from enum import Enum
import base64
import sys
from pathlib import Path

from .import_helper import setup_project_paths

from config.constants import SUPPORTED_IMAGE_EXTS
# from config.settings import FAST_CONFIG  # TODO: Check if FAST_CONFIG exists

logger = logging.getLogger(__name__)


class ImageFormat(Enum):
    """圖像格式列舉"""
    JPEG = ".jpg"
    PNG = ".png"
    BMP = ".bmp"
    TIFF = ".tiff"
    WEBP = ".webp"


@dataclass
class ImageInfo:
    """圖像信息數據結構"""
    width: int
    height: int
    channels: int
    dtype: str
    size_bytes: int
    format: str | None = None
    color_space: str | None = None
    has_alpha: bool = False


@dataclass
class PreviewConfig:
    """預覽配置"""
    target_width: int = 1536
    target_height: int = 256
    quality: int = FAST_CONFIG.processing.image_quality
    layout: str = "horizontal"  # horizontal, grid, custom


class ImageUtils:
    """
    圖像處理工具類
    
    提供各種圖像處理的便利方法。
    """
    
    @staticmethod
    def cv_imread(file_path: str, flags: int = cv2.IMREAD_UNCHANGED) -> np.ndarray | None:
        """
        使用 OpenCV 讀取圖像（支援中文路徑）
        
        Args:
            file_path: 圖像文件路徑
            flags: OpenCV 讀取標誌
            
        Returns:
            圖像數組，失敗返回 None
        """
        try:
            # 快速檢查副檔名
            ext = os.path.splitext(file_path)[1].lower()
            if ext not in SUPPORTED_IMAGE_EXTS:
                logger.warning(f"不支援的圖像格式: {file_path}")
                return None
                
            # 使用 np.fromfile 支援中文路徑
            img_data = np.fromfile(file_path, dtype=np.uint8)
            cv_img = cv2.imdecode(img_data, flags)
            # cv_img = cv2.cvtColor(cv_img, cv2.COLOR_BGR2RGB) if cv_img is not None else None
            
            if cv_img is None:
                logger.warning(f"無法解碼圖像: {file_path}")
                
            return cv_img
            
        except Exception as e:
            logger.warning(f"讀取圖像 {file_path} 時發生錯誤: {e}")
            return None
            
    @staticmethod
    def cv_imwrite(file_path: str, image: np.ndarray,
                  quality: int = FAST_CONFIG.processing.image_quality,
                  params: list[int] | None = None) -> bool:
        """
        使用 OpenCV 保存圖像（支援中文路徑）
        
        Args:
            file_path: 保存路徑
            image: 圖像數組
            quality: 圖像質量
            params: 額外的編碼參數
            
        Returns:
            是否成功
        """
        try:
            # 確保目錄存在
            os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
            
            # 根據副檔名設置參數
            ext = os.path.splitext(file_path)[1].lower()
            
            if params is None:
                if ext in ['.jpg', '.jpeg']:
                    params = [cv2.IMWRITE_JPEG_QUALITY, quality]
                elif ext == '.png':
                    # PNG 壓縮級別
                    compress_level = min(9, max(0, int(9 - quality / 10)))
                    params = [cv2.IMWRITE_PNG_COMPRESSION, compress_level]
                elif ext == '.webp':
                    params = [cv2.IMWRITE_WEBP_QUALITY, quality]
                else:
                    params = []
                    
            # 編碼和保存
            success, encoded = cv2.imencode(ext, image, params)
            
            if success:
                encoded.tofile(file_path)
                return True
            else:
                logger.error(f"圖像編碼失敗: {file_path}")
                return False
                
        except Exception as e:
            logger.error(f"保存圖像 {file_path} 時發生錯誤: {e}")
            return False
            
    @staticmethod
    def is_valid_image(file_path: str, check_content: bool = True) -> bool:
        """
        檢查文件是否為有效的圖像文件
        
        Args:
            file_path: 文件路徑
            check_content: 是否檢查文件內容
            
        Returns:
            是否為有效圖像
        """
        try:
            # 檢查文件存在性
            if not os.path.exists(file_path):
                return False
                
            # 檢查副檔名
            ext = os.path.splitext(file_path)[1].lower()
            if ext not in SUPPORTED_IMAGE_EXTS:
                return False
                
            if not check_content:
                return True
                
            # 使用 imghdr 檢查文件類型
            img_type = imghdr.what(file_path)
            if img_type is None:
                return False
                
            # 嘗試使用 PIL 驗證
            try:
                with Image.open(file_path) as img:
                    img.verify()
                return True
            except (OSError, UnidentifiedImageError):
                return False
                
        except Exception as e:
            logger.debug(f"驗證圖像時發生錯誤 {file_path}: {e}")
            return False
            
    @staticmethod
    def get_image_info(image: str | np.ndarray) -> ImageInfo | None:
        """
        獲取圖像信息
        
        Args:
            image: 圖像路徑或圖像數組
            
        Returns:
            圖像信息對象
        """
        try:
            if isinstance(image, str):
                # 從文件路徑獲取信息
                if not os.path.exists(image):
                    return None
                    
                # 使用 PIL 獲取基本信息
                with Image.open(image) as img:
                    width, height = img.size
                    format_name = img.format
                    channels = len(img.getbands())
                    has_alpha = 'A' in img.getbands()
                    
                # 文件大小
                size_bytes = os.path.getsize(image)
                
                return ImageInfo(
                    width=width,
                    height=height,
                    channels=channels,
                    dtype="unknown",
                    size_bytes=size_bytes,
                    format=format_name,
                    has_alpha=has_alpha
                )
                
            elif isinstance(image, np.ndarray):
                # 從數組獲取信息
                if len(image.shape) == 2:
                    height, width = image.shape
                    channels = 1
                elif len(image.shape) == 3:
                    height, width, channels = image.shape
                else:
                    return None
                    
                size_bytes = image.nbytes
                has_alpha = channels == 4
                
                return ImageInfo(
                    width=width,
                    height=height,
                    channels=channels,
                    dtype=str(image.dtype),
                    size_bytes=size_bytes,
                    has_alpha=has_alpha
                )
                
        except Exception as e:
            logger.error(f"獲取圖像信息失敗: {e}")
            return None
            
    @staticmethod
    def resize_image(image: np.ndarray, target_size: tuple[int, int],
                    interpolation: int = cv2.INTER_LINEAR,
                    keep_aspect_ratio: bool = False) -> np.ndarray:
        """
        調整圖像大小
        
        Args:
            image: 輸入圖像
            target_size: 目標大小 (width, height)
            interpolation: 插值方法
            keep_aspect_ratio: 是否保持寬高比
            
        Returns:
            調整後的圖像
        """
        try:
            if keep_aspect_ratio:
                # 保持寬高比的縮放
                h, w = image.shape[:2]
                target_w, target_h = target_size
                
                # 計算縮放比例
                scale = min(target_w / w, target_h / h)
                new_w = int(w * scale)
                new_h = int(h * scale)
                
                # 先縮放
                resized = cv2.resize(image, (new_w, new_h), interpolation=interpolation)
                
                # 創建目標大小的畫布
                if len(image.shape) == 3:
                    canvas = np.zeros((target_h, target_w, image.shape[2]), dtype=image.dtype)
                else:
                    canvas = np.zeros((target_h, target_w), dtype=image.dtype)
                    
                # 居中放置
                y_offset = (target_h - new_h) // 2
                x_offset = (target_w - new_w) // 2
                
                if len(image.shape) == 3:
                    canvas[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized
                else:
                    canvas[y_offset:y_offset+new_h, x_offset:x_offset+new_w] = resized
                    
                return canvas
            else:
                # 直接縮放到目標大小
                return cv2.resize(image, target_size, interpolation=interpolation)
                
        except Exception as e:
            logger.error(f"調整圖像大小失敗: {e}")
            return image
            
    @staticmethod
    def convert_color_space(image: np.ndarray, source: str, target: str) -> np.ndarray:
        """
        轉換圖像色彩空間
        
        Args:
            image: 輸入圖像
            source: 來源色彩空間
            target: 目標色彩空間
            
        Returns:
            轉換後的圖像
        """
        try:
            # 色彩空間轉換映射
            conversion_map = {
                ('BGR', 'RGB'): cv2.COLOR_BGR2RGB,
                ('RGB', 'BGR'): cv2.COLOR_RGB2BGR,
                ('BGR', 'GRAY'): cv2.COLOR_BGR2GRAY,
                ('RGB', 'GRAY'): cv2.COLOR_RGB2GRAY,
                ('GRAY', 'BGR'): cv2.COLOR_GRAY2BGR,
                ('GRAY', 'RGB'): cv2.COLOR_GRAY2RGB,
                ('BGR', 'HSV'): cv2.COLOR_BGR2HSV,
                ('RGB', 'HSV'): cv2.COLOR_RGB2HSV,
                ('HSV', 'BGR'): cv2.COLOR_HSV2BGR,
                ('HSV', 'RGB'): cv2.COLOR_HSV2RGB,
            }
            
            key = (source.upper(), target.upper())
            if key in conversion_map:
                return cv2.cvtColor(image, conversion_map[key])
            else:
                logger.warning(f"不支援的色彩空間轉換: {source} -> {target}")
                return image
                
        except Exception as e:
            logger.error(f"色彩空間轉換失敗: {e}")
            return image
            
    @staticmethod
    def validate_image_dimensions(image: np.ndarray,
                                min_size: tuple[int, int] | None = None,
                                max_size: tuple[int, int] | None = None) -> bool:
        """
        驗證圖像尺寸是否在指定範圍內
        
        Args:
            image: 圖像數組
            min_size: 最小尺寸 (width, height)
            max_size: 最大尺寸 (width, height)
            
        Returns:
            是否符合要求
        """
        try:
            h, w = image.shape[:2]
            
            if min_size:
                min_w, min_h = min_size
                if w < min_w or h < min_h:
                    return False
                    
            if max_size:
                max_w, max_h = max_size
                if w > max_w or h > max_h:
                    return False
                    
            return True
            
        except Exception:
            return False


class PreviewGenerator:
    """
    預覽圖像生成器
    
    用於生成各種格式的預覽圖像。
    """
    
    def __init__(self, config: PreviewConfig | None = None):
        """
        初始化預覽生成器
        
        Args:
            config: 預覽配置
        """
        self.config = config or PreviewConfig()
        
    def create_cube_preview(self, cube_dict: dict[str, np.ndarray]) -> np.ndarray:
        """
        從立方體面字典創建預覽圖像
        
        Args:
            cube_dict: 立方體面字典
            
        Returns:
            預覽圖像
        """
        try:
            if not cube_dict:
                logger.warning("立方體字典為空")
                return np.zeros((self.config.target_height, self.config.target_width, 3), dtype=np.uint8)
                
            # 獲取第一個面的尺寸
            first_face = next(iter(cube_dict.values()))
            face_h, face_w = first_face.shape[:2]
            
            if self.config.layout == "horizontal":
                # 水平排列
                preview = np.zeros((face_h, face_w * 6, 3), dtype=np.uint8)
                face_order = ["F", "R", "B", "L", "U", "D"]
                
                for i, face_key in enumerate(face_order):
                    if face_key in cube_dict:
                        face = cube_dict[face_key]
                        
                        # 確保面的尺寸正確
                        if face.shape[:2] != (face_h, face_w):
                            face = cv2.resize(face, (face_w, face_h))
                            
                        # 確保是3通道圖像
                        if len(face.shape) == 2:
                            face = cv2.cvtColor(face, cv2.COLOR_GRAY2BGR)
                        elif face.shape[2] == 4:
                            face = cv2.cvtColor(face, cv2.COLOR_BGRA2BGR)
                            
                        preview[:, i*face_w:(i+1)*face_w] = face
                        
            elif self.config.layout == "grid":
                # 2x3 網格排列
                preview = self._create_grid_preview(cube_dict, face_h, face_w)
            else:
                # 默認使用水平排列
                return self.create_cube_preview(cube_dict)
                
            # 調整到目標尺寸
            if preview.shape[:2] != (self.config.target_height, self.config.target_width):
                preview = cv2.resize(
                    preview, 
                    (self.config.target_width, self.config.target_height),
                    interpolation=cv2.INTER_LINEAR
                )
                
            return preview
            
        except Exception as e:
            logger.error(f"創建立方體預覽失敗: {e}")
            return np.zeros((self.config.target_height, self.config.target_width, 3), dtype=np.uint8)
            
    def _create_grid_preview(self, cube_dict: dict[str, np.ndarray],
                           face_h: int, face_w: int) -> np.ndarray:
        """創建網格布局預覽"""
        # 2x3 網格布局
        preview = np.zeros((face_h * 2, face_w * 3, 3), dtype=np.uint8)
        
        # 網格位置映射
        grid_positions = {
            "F": (0, 1),  # 前面
            "R": (0, 2),  # 右面  
            "B": (1, 2),  # 後面
            "L": (0, 0),  # 左面
            "U": (1, 1),  # 上面
            "D": (1, 0)   # 下面
        }
        
        for face_key, (row, col) in grid_positions.items():
            if face_key in cube_dict:
                face = cube_dict[face_key]
                
                # 處理圖像格式
                if face.shape[:2] != (face_h, face_w):
                    face = cv2.resize(face, (face_w, face_h))
                    
                if len(face.shape) == 2:
                    face = cv2.cvtColor(face, cv2.COLOR_GRAY2BGR)
                elif face.shape[2] == 4:
                    face = cv2.cvtColor(face, cv2.COLOR_BGRA2BGR)
                    
                # 放置到網格中
                y_start = row * face_h
                y_end = (row + 1) * face_h
                x_start = col * face_w
                x_end = (col + 1) * face_w
                
                preview[y_start:y_end, x_start:x_end] = face
                
        return preview
        
    def create_image_thumbnail(self, image: np.ndarray,
                             thumbnail_size: tuple[int, int] = (400, 200)) -> np.ndarray:
        """
        創建圖像縮略圖
        
        Args:
            image: 輸入圖像
            thumbnail_size: 縮略圖尺寸
            
        Returns:
            縮略圖
        """
        try:
            return ImageUtils.resize_image(
                image, thumbnail_size, 
                interpolation=cv2.INTER_AREA,
                keep_aspect_ratio=True
            )
        except Exception as e:
            logger.error(f"創建縮略圖失敗: {e}")
            return np.zeros((thumbnail_size[1], thumbnail_size[0], 3), dtype=np.uint8)


class ImageBatch:
    """
    批次圖像處理器
    
    用於批次處理多張圖像。
    """
    
    @staticmethod
    def load_images(image_paths: list[str],
                   max_workers: int = 4) -> list[tuple[str, np.ndarray | None]]:
        """
        批次載入圖像
        
        Args:
            image_paths: 圖像路徑列表
            max_workers: 最大並行數
            
        Returns:
            (圖像ID, 圖像數組) 元組列表
        """
        results = []
        
        for img_path in image_paths:
            img_id = os.path.splitext(os.path.basename(img_path))[0]
            image = ImageUtils.cv_imread(img_path)
            results.append((img_id, image))
            
        return results
        
    @staticmethod
    def validate_images(image_paths: list[str]) -> tuple[list[str], list[str]]:
        """
        批次驗證圖像文件
        
        Args:
            image_paths: 圖像路徑列表
            
        Returns:
            (有效圖像列表, 無效圖像列表)
        """
        valid_images = []
        invalid_images = []
        
        for img_path in image_paths:
            if ImageUtils.is_valid_image(img_path):
                valid_images.append(img_path)
            else:
                invalid_images.append(img_path)
                
        return valid_images, invalid_images
        
    @staticmethod
    def get_images_info(image_paths: list[str]) -> dict[str, ImageInfo]:
        """
        批次獲取圖像信息
        
        Args:
            image_paths: 圖像路徑列表
            
        Returns:
            圖像信息字典
        """
        info_dict = {}
        
        for img_path in image_paths:
            img_id = os.path.basename(img_path)
            info = ImageUtils.get_image_info(img_path)
            if info:
                info_dict[img_id] = info
                
        return info_dict


class ImageEncoder:
    """
    圖像編碼器
    
    處理圖像的編碼和解碼操作。
    """
    
    @staticmethod
    def image_to_base64(image_path: str) -> str | None:
        """
        將圖像文件轉換為 base64 編碼
        
        Args:
            image_path: 圖像路徑
            
        Returns:
            base64 編碼字符串
        """
        try:
            if not os.path.exists(image_path):
                return None
                
            with open(image_path, 'rb') as img_file:
                return base64.b64encode(img_file.read()).decode('utf-8')
                
        except Exception as e:
            logger.error(f"轉換圖像為 base64 失敗 {image_path}: {e}")
            return None
            
    @staticmethod
    def base64_to_image(base64_data: str, output_path: str) -> bool:
        """
        將 base64 數據保存為圖像文件
        
        Args:
            base64_data: base64 編碼數據
            output_path: 輸出圖像路徑
            
        Returns:
            是否成功
        """
        try:
            # 解碼 base64 數據
            image_data = base64.b64decode(base64_data)
            
            # 確保目錄存在
            os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
            
            # 保存圖像
            with open(output_path, 'wb') as img_file:
                img_file.write(image_data)
                
            return True
            
        except Exception as e:
            logger.error(f"保存 base64 圖像失敗 {output_path}: {e}")
            return False
            
    @staticmethod
    def array_to_base64(image_array: np.ndarray, format: str = '.jpg',
                       quality: int = 95) -> str | None:
        """
        將圖像數組轉換為 base64 編碼
        
        Args:
            image_array: 圖像數組
            format: 圖像格式
            quality: 圖像質量
            
        Returns:
            base64 編碼字符串
        """
        try:
            # 編碼圖像
            if format.lower() in ['.jpg', '.jpeg']:
                params = [cv2.IMWRITE_JPEG_QUALITY, quality]
            elif format.lower() == '.png':
                params = [cv2.IMWRITE_PNG_COMPRESSION, 9]
            else:
                params = []
                
            success, encoded = cv2.imencode(format, image_array, params)
            
            if success:
                return base64.b64encode(encoded.tobytes()).decode('utf-8')
            else:
                return None
                
        except Exception as e:
            logger.error(f"圖像數組轉 base64 失敗: {e}")
            return None


def check_opencv_cuda() -> bool:
    """檢查 OpenCV CUDA 是否可用"""
    try:
        return cv2.cuda.getCudaEnabledDeviceCount() > 0
    except:
        return False


def main():
    """
    模組使用示例
    """
    print("=== 圖像處理工具模組 ===")
    print("\n功能說明：")
    print("1. 圖像讀寫和格式轉換")
    print("2. 圖像驗證和信息獲取")
    print("3. 預覽圖像生成")
    print("4. 批次圖像處理")
    print("5. 圖像編碼/解碼")
    print("6. 尺寸調整和處理")
    
    print("\n使用示例：")
    print("```python")
    print("from utils.image_utils import ImageUtils, PreviewGenerator, ImageBatch")
    print("from utils.image_utils import PreviewConfig, ImageEncoder")
    print("import cv2")
    print()
    print("# 基本圖像操作")
    print("# 讀取圖像")
    print("image = ImageUtils.cv_imread('panorama.jpg')")
    print("if image is not None:")
    print("    print(f'圖像尺寸: {image.shape}')")
    print()
    print("# 獲取圖像信息")
    print("info = ImageUtils.get_image_info('panorama.jpg')")
    print("if info:")
    print("    print(f'尺寸: {info.width}x{info.height}, 通道: {info.channels}')")
    print()
    print("# 驗證圖像")
    print("is_valid = ImageUtils.is_valid_image('panorama.jpg')")
    print("print(f'圖像有效: {is_valid}')")
    print()
    print("# 調整圖像大小")
    print("resized = ImageUtils.resize_image(image, (1024, 512), keep_aspect_ratio=True)")
    print()
    print("# 色彩空間轉換")
    print("rgb_image = ImageUtils.convert_color_space(image, 'BGR', 'RGB')")
    print()
    print("# 預覽圖像生成")
    print("config = PreviewConfig(target_width=1536, target_height=256, layout='horizontal')")
    print("generator = PreviewGenerator(config)")
    print()
    print("# 立方體預覽")
    print("cube_faces = {")
    print("    'F': cv2.imread('front.jpg'),")
    print("    'R': cv2.imread('right.jpg'),")
    print("    # ... 其他面")
    print("}")
    print("preview = generator.create_cube_preview(cube_faces)")
    print()
    print("# 創建縮略圖")
    print("thumbnail = generator.create_image_thumbnail(image, (400, 200))")
    print()
    print("# 批次處理")
    print("image_paths = ['img1.jpg', 'img2.jpg', 'img3.jpg']")
    print()
    print("# 批次載入")
    print("loaded_images = ImageBatch.load_images(image_paths)")
    print("print(f'載入 {len(loaded_images)} 張圖像')")
    print()
    print("# 批次驗證")
    print("valid, invalid = ImageBatch.validate_images(image_paths)")
    print("print(f'有效: {len(valid)}, 無效: {len(invalid)}')")
    print()
    print("# 批次信息獲取")
    print("images_info = ImageBatch.get_images_info(image_paths)")
    print()
    print("# 圖像編碼")
    print("# 轉換為 base64")
    print("base64_data = ImageEncoder.image_to_base64('panorama.jpg')")
    print("if base64_data:")
    print("    print(f'Base64 長度: {len(base64_data)} 字符')")
    print()
    print("# 從 base64 保存")
    print("ImageEncoder.base64_to_image(base64_data, 'decoded.jpg')")
    print()
    print("# 數組轉 base64")
    print("array_base64 = ImageEncoder.array_to_base64(image, '.jpg', 90)")
    print("```")
    
    print("\n主要類別：")
    print("- ImageUtils: 基本圖像操作工具")
    print("- PreviewGenerator: 預覽圖像生成器")
    print("- ImageBatch: 批次圖像處理器")
    print("- ImageEncoder: 圖像編碼器")
    print("- ImageInfo: 圖像信息數據結構")
    print("- PreviewConfig: 預覽配置")
    
    # 簡單測試
    print("\n測試示例：")
    
    # 創建測試圖像
    test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    # 測試圖像信息獲取
    info = ImageUtils.get_image_info(test_image)
    if info:
        print(f"測試圖像信息: {info.width}x{info.height}, {info.channels}通道, {info.dtype}")
        
    # 測試尺寸調整
    resized = ImageUtils.resize_image(test_image, (320, 240))
    print(f"尺寸調整測試: {test_image.shape} -> {resized.shape}")
    
    # 測試色彩空間轉換
    gray = ImageUtils.convert_color_space(test_image, 'BGR', 'GRAY')
    print(f"色彩轉換測試: {test_image.shape} -> {gray.shape}")
    
    # 測試預覽生成器
    generator = PreviewGenerator()
    
    # 模擬立方體面數據
    cube_data = {f: test_image for f in ['F', 'R', 'B', 'L', 'U', 'D']}
    preview = generator.create_cube_preview(cube_data)
    print(f"立方體預覽測試: {preview.shape}")
    
    # 測試縮略圖
    thumbnail = generator.create_image_thumbnail(test_image)
    print(f"縮略圖測試: {thumbnail.shape}")
    
    # 測試 OpenCV CUDA
    cuda_available = check_opencv_cuda()
    print(f"OpenCV CUDA 可用: {cuda_available}")
    
    print("圖像處理工具測試完成")


if __name__ == "__main__":
    main()
