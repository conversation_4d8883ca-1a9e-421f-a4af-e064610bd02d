from __future__ import annotations

"""
投影後端實現模組 (Projection Backends Module)

本模組提供了投影轉換的高效實現。
支援 PyTorch (CUDA) 後端，並在不可用時回退到 CPU 實現。
移除了 CuPy 相關功能，改用純 NumPy 實現。
"""

import warnings
from typing import TYPE_CHECKING, Optional, Tuple

import cv2
import numpy as np
from numpy.typing import NDArray

# --- GPU 加速庫的選擇性導入 ---
try:
    import torch
    import torch.nn.functional as F
    HAS_TORCH = True
except ImportError:
    HAS_TORCH = False

try:
    import pyopencl as cl
    import pyopencl.array as cl_array
    HAS_OPENCL = True
except ImportError:
    HAS_OPENCL = False

# 為了類型提示，即使在沒有安裝的環境下也能正常工作
if TYPE_CHECKING:
    from .core import ProjectionCore

from src.log_utils.factory import get_logger

logger = get_logger(__name__)


class ProjectionBackend:
    """
    管理所有投影後端實現的類別。

    提供高效的投影轉換實現，支援 PyTorch CUDA 後端，
    並在不可用時回退到優化的 NumPy CPU 實現。
    """

    def __init__(self, projection_core: ProjectionCore):
        """
        初始化投影後端。

        Args:
            projection_core: `ProjectionCore` 的實例，用於存取共享的設定和回退到 CPU 實現。
        """
        self.core = projection_core

    def equirect_to_cubemap_pytorch(self, e_img: NDArray, squeeze: bool) -> NDArray:
        """
        使用 PyTorch (CUDA) 實現的全景圖到立方體圖的轉換。

        Args:
            e_img: 輸入的全景圖 NumPy 陣列。
            squeeze: 指示原始圖像是否為單通道。

        Returns:
            轉換後的水平排列立方體圖 NumPy 陣列。
        """
        if not HAS_TORCH:
            return self.core._equirect_to_cubemap_cpu(e_img)

        try:
            device = f"cuda:{self.core.device_info.device_id}"
            # 1. 將 NumPy 陣列轉移到指定的 GPU 設備
            if e_img.dtype == np.uint8:
                e_tensor = torch.from_numpy(e_img.astype(np.float32) / 255.0).to(device)
            else:
                e_tensor = torch.from_numpy(e_img.astype(np.float32)).to(device)

            # 2. 調整維度順序以符合 PyTorch 的 (C, H, W) 格式
            if e_tensor.dim() == 3:
                e_tensor = e_tensor.permute(2, 0, 1)

            # 3. 核心轉換邏輯
            cubemap_tensor = self._generate_cubemap_pytorch(e_tensor)

            # 4. 將維度順序轉換回 (H, W, C)
            if cubemap_tensor.dim() == 4:
                cubemap_tensor = cubemap_tensor.permute(0, 2, 3, 1)

            # 5. 將結果從 GPU 轉移回 CPU，並拼接成水平排列格式
            cubemap_faces = cubemap_tensor.cpu().numpy()
            cubemap = np.concatenate([cubemap_faces[i] for i in range(6)], axis=1)

            # 6. 轉換回原始資料類型
            if e_img.dtype == np.uint8:
                cubemap = (cubemap * 255.0).clip(0, 255).astype(np.uint8)
            else:
                cubemap = cubemap.astype(e_img.dtype)

            return cubemap

        except Exception as e:
            logger.warning(f"PyTorch (CUDA) 處理失敗，回退到 CPU: {e}")
            return self.core._equirect_to_cubemap_cpu(e_img)

    def _generate_cubemap_pytorch(self, e_tensor: torch.Tensor) -> torch.Tensor:
        """使用 PyTorch (CUDA) 執行從全景圖到立方體圖的採樣。"""
        device, dtype = e_tensor.device, e_tensor.dtype
        c, h, w = e_tensor.shape
        face_size = self.core.face_w

        # 在 GPU 上生成目標立方體面的 3D 座標
        xyz_coords = self._generate_cube_coords_pytorch(face_size, device, dtype)
        # 將 3D 座標轉換為球面座標 (u, v)
        u, v = self._xyz_to_uv_pytorch(xyz_coords)
        # 將球面座標轉換為來源全景圖的像素座標
        coor_x, coor_y = self._uv_to_coor_pytorch(u, v, h, w)
        # 根據計算出的座標，從來源全景圖中採樣像素
        cubemap_faces = self._sample_equirect_pytorch(e_tensor, coor_x, coor_y)

        return cubemap_faces

    def _generate_cube_coords_pytorch(self, face_size: int, device: torch.device, dtype: torch.dtype) -> torch.Tensor:
        """在 PyTorch 中生成立方體六個面的 3D 座標。"""
        rng = torch.linspace(-0.5, 0.5, face_size, device=device, dtype=dtype)
        y_grid, x_grid = torch.meshgrid(rng, rng, indexing="ij")
        x_grid, y_grid = x_grid.flip(1), -y_grid

        faces = [
            torch.stack([x_grid, y_grid, torch.full_like(x_grid, 0.5)], dim=-1),  # Front
            torch.stack([torch.full_like(x_grid, 0.5), y_grid, -x_grid], dim=-1),  # Right
            torch.stack([-x_grid, y_grid, torch.full_like(x_grid, -0.5)], dim=-1),  # Back
            torch.stack([torch.full_like(x_grid, -0.5), y_grid, x_grid], dim=-1),  # Left
            torch.stack([x_grid, torch.full_like(x_grid, 0.5), -y_grid], dim=-1),  # Up
            torch.stack([x_grid, torch.full_like(x_grid, -0.5), y_grid], dim=-1),  # Down
        ]
        return torch.stack(faces, dim=0)

    def _xyz_to_uv_pytorch(self, xyz: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """在 PyTorch 中將 3D 笛卡爾座標轉換為球面座標。"""
        x, y, z = xyz[..., 0], xyz[..., 1], xyz[..., 2]
        u = torch.atan2(x, z)
        v = torch.atan2(y, torch.hypot(x, z))
        return u, v

    def _uv_to_coor_pytorch(self, u: torch.Tensor, v: torch.Tensor, h: int, w: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """在 PyTorch 中將球面座標轉換為像素座標。"""
        coor_x = (u / (2 * torch.pi) + 0.5) * w - 0.5
        coor_y = (-v / torch.pi + 0.5) * h - 0.5
        return coor_x, coor_y

    def _sample_equirect_pytorch(self, e_tensor: torch.Tensor, coor_x: torch.Tensor, coor_y: torch.Tensor) -> torch.Tensor:
        """使用 PyTorch 的 `grid_sample` 函數高效地從全景圖中採樣。"""
        c, h, w = e_tensor.shape
        # 將像素座標正規化到 [-1, 1] 的範圍，以符合 `grid_sample` 的要求
        grid_x = 2.0 * coor_x / (w - 1) - 1.0
        grid_y = 2.0 * coor_y / (h - 1) - 1.0
        grid = torch.stack([grid_x, grid_y], dim=-1)

        cubemap_faces = []
        for face_idx in range(6):
            face_grid = grid[face_idx].unsqueeze(0)
            # `grid_sample` 需要 (N, C, H_in, W_in) 的輸入和 (N, H_out, W_out, 2) 的網格
            sampled = F.grid_sample(
                e_tensor.unsqueeze(0),
                face_grid,
                mode="bilinear",
                padding_mode="border",
                align_corners=True,
            )
            cubemap_faces.append(sampled.squeeze(0))

        return torch.stack(cubemap_faces, dim=0)

    def equirect_to_cubemap_numpy(self, e_img: NDArray, squeeze: bool) -> NDArray:
        """
        使用純 NumPy 實現的全景圖到立方體圖的轉換。

        Args:
            e_img: 輸入的全景圖 NumPy 陣列。
            squeeze: 指示原始圖像是否為單通道。

        Returns:
            轉換後的水平排列立方體圖 NumPy 陣列。
        """
        try:
            cubemap_faces = self._generate_cubemap_numpy(e_img)
            cubemap = np.concatenate([cubemap_faces[i] for i in range(6)], axis=1)
            return cubemap
        except Exception as e:
            logger.warning(f"NumPy 處理失敗，回退到 CPU: {e}")
            return self.core._equirect_to_cubemap_cpu(e_img)

    def _generate_cubemap_numpy(self, e_img: NDArray) -> NDArray:
        """使用 NumPy 執行從全景圖到立方體圖的採樣。"""
        h, w = e_img.shape[:2]
        channels = e_img.shape[2] if e_img.ndim == 3 else 1
        face_size = self.core.face_w

        xyz_coords = self._generate_cube_coords_numpy(face_size)
        u, v = self._xyz_to_uv_numpy(xyz_coords)
        coor_x, coor_y = self._uv_to_coor_numpy(u, v, h, w)

        cubemap_faces_shape = (6, face_size, face_size, channels) if channels > 1 else (6, face_size, face_size)
        cubemap_faces = np.zeros(cubemap_faces_shape, dtype=e_img.dtype)

        for i in range(6):
            cubemap_faces[i] = self._sample_equirect_numpy(e_img, coor_x[i], coor_y[i])

        return cubemap_faces

    def _generate_cube_coords_numpy(self, face_size: int) -> NDArray:
        """在 NumPy 中生成立方體六個面的 3D 座標。"""
        rng = np.linspace(-0.5, 0.5, face_size, dtype=np.float32)
        y_grid, x_grid = np.meshgrid(rng, rng, indexing="ij")
        x_grid, y_grid = np.flip(x_grid, axis=1), -y_grid

        faces = [
            np.stack([x_grid, y_grid, np.full_like(x_grid, 0.5)], axis=-1),
            np.stack([np.full_like(x_grid, 0.5), y_grid, -x_grid], axis=-1),
            np.stack([-x_grid, y_grid, np.full_like(x_grid, -0.5)], axis=-1),
            np.stack([np.full_like(x_grid, -0.5), y_grid, x_grid], axis=-1),
            np.stack([x_grid, np.full_like(x_grid, 0.5), -y_grid], axis=-1),
            np.stack([x_grid, np.full_like(x_grid, -0.5), y_grid], axis=-1),
        ]
        return np.stack(faces, axis=0)

    def _xyz_to_uv_numpy(self, xyz: NDArray) -> Tuple[NDArray, NDArray]:
        """在 NumPy 中將 3D 笛卡爾座標轉換為球面座標。"""
        x, y, z = xyz[..., 0], xyz[..., 1], xyz[..., 2]
        u = np.arctan2(x, z)
        v = np.arctan2(y, np.hypot(x, z))
        return u, v

    def _uv_to_coor_numpy(self, u: NDArray, v: NDArray, h: int, w: int) -> Tuple[NDArray, NDArray]:
        """在 NumPy 中將球面座標轉換為像素座標。"""
        coor_x = (u / (2 * np.pi) + 0.5) * w - 0.5
        coor_y = (-v / np.pi + 0.5) * h - 0.5
        return coor_x, coor_y

    def _sample_equirect_numpy(self, e_img: NDArray, coor_x: NDArray, coor_y: NDArray) -> NDArray:
        """在 NumPy 中手動實現雙線性插值採樣。"""
        h, w = e_img.shape[:2]
        coor_x, coor_y = np.clip(coor_x, 0, w - 1), np.clip(coor_y, 0, h - 1)
        x0, y0 = np.floor(coor_x).astype(np.int32), np.floor(coor_y).astype(np.int32)
        x1, y1 = np.minimum(x0 + 1, w - 1), np.minimum(y0 + 1, h - 1)
        wx, wy = coor_x - x0, coor_y - y0

        if e_img.ndim == 3:
            result = np.zeros((*coor_x.shape, e_img.shape[2]), dtype=e_img.dtype)
            for c in range(e_img.shape[2]):
                i00, i01 = e_img[y0, x0, c], e_img[y0, x1, c]
                i10, i11 = e_img[y1, x0, c], e_img[y1, x1, c]
                result[..., c] = i00*(1-wx)*(1-wy) + i01*wx*(1-wy) + i10*(1-wx)*wy + i11*wx*wy
        else:
            i00, i01 = e_img[y0, x0], e_img[y0, x1]
            i10, i11 = e_img[y1, x0], e_img[y1, x1]
            result = i00*(1-wx)*(1-wy) + i01*wx*(1-wy) + i10*(1-wx)*wy + i11*wx*wy

        return result


# 為了向後相容性，提供舊的類名別名
GPUBackend = ProjectionBackend